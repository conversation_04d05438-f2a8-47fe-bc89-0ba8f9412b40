using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 段落格式匹配规则编辑窗体 - 方法部分，包含控件创建、数据加载保存等核心功能方法
    /// </summary>
    public partial class ParagraphFormatRuleEditForm
    {
        #region 字体格式控件创建方法

        /// <summary>
        /// 创建字体颜色控件
        /// </summary>
        private void CreateFontColorControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "字体颜色",
                Dock = DockStyle.Fill,
                Height = 110,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));

            _chkSetFontColor = new CheckBox
            {
                Text = "设置字体颜色",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _btnFontColor = new Button
            {
                Text = "选择颜色",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                BackColor = Color.Black,
                ForeColor = Color.White,
                Margin = new Padding(5, 8, 5, 8)
            };

            panel.Controls.Add(_chkSetFontColor, 0, 0);
            panel.Controls.Add(_btnFontColor, 1, 0);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建下划线控件
        /// </summary>
        private void CreateUnderlineControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "下划线设置",
                Dock = DockStyle.Fill,
                Height = 150,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 2,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));

            _chkSetUnderline = new CheckBox
            {
                Text = "设置下划线",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _cmbUnderlineType = new CenteredComboBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(2)
            };
            _cmbUnderlineType.Items.AddRange(new string[] { "无", "单下划线", "双下划线", "粗下划线", "点状下划线", "虚线下划线", "点划线下划线", "双点划线下划线", "波浪线下划线" });

            _btnUnderlineColor = new Button
            {
                Text = "颜色",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                BackColor = Color.Black,
                ForeColor = Color.White,
                Margin = new Padding(2)
            };

            panel.Controls.Add(_chkSetUnderline, 0, 0);
            panel.SetColumnSpan(_chkSetUnderline, 4);
            panel.Controls.Add(new Label { Text = "下划线类型:", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 0, 1);
            panel.Controls.Add(_cmbUnderlineType, 1, 1);
            panel.Controls.Add(_btnUnderlineColor, 2, 1);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建文字效果控件
        /// </summary>
        private void CreateTextEffectsControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "文字效果",
                Dock = DockStyle.Fill,
                Height = 230,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 5,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50));
            for (int i = 0; i < 5; i++)
            {
                panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));
            }

            _chkSetTextEffects = new CheckBox
            {
                Text = "设置文字效果",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _chkStrikethrough = new CheckBox
            {
                Text = "删除线",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _chkDoubleStrikethrough = new CheckBox
            {
                Text = "双删除线",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _chkSuperscript = new CheckBox
            {
                Text = "上标",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _chkSubscript = new CheckBox
            {
                Text = "下标",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            panel.Controls.Add(_chkSetTextEffects, 0, 0);
            panel.SetColumnSpan(_chkSetTextEffects, 2);
            panel.Controls.Add(_chkStrikethrough, 0, 1);
            panel.Controls.Add(_chkDoubleStrikethrough, 1, 1);
            panel.Controls.Add(_chkSuperscript, 0, 2);
            panel.Controls.Add(_chkSubscript, 1, 2);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        #endregion

        #region 事件处理器设置

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 规则名称文本框事件
            _txtRuleName.TextChanged += TxtRuleName_TextChanged;

            // 匹配条件事件
            _chkStartsWith.CheckedChanged += ChkStartsWith_CheckedChanged;
            _chkContains.CheckedChanged += ChkContains_CheckedChanged;
            _chkEndsWith.CheckedChanged += ChkEndsWith_CheckedChanged;
            _chkRegex.CheckedChanged += ChkRegex_CheckedChanged;
            _chkCharacterCount.CheckedChanged += ChkCharacterCount_CheckedChanged;

            // 关键词管理事件
            _btnAddKeyword.Click += BtnAddKeyword_Click;
            _btnRemoveKeyword.Click += BtnRemoveKeyword_Click;
            _txtNewKeyword.KeyPress += TxtNewKeyword_KeyPress;

            // 段落格式事件
            _chkEnableParagraphFormat.CheckedChanged += ChkEnableParagraphFormat_CheckedChanged;
            _chkEnableIndentation.CheckedChanged += ChkEnableIndentation_CheckedChanged;
            _chkEnableSpacing.CheckedChanged += ChkEnableSpacing_CheckedChanged;
            _chkEnableChineseControl.CheckedChanged += ChkEnableChineseControl_CheckedChanged;
            _cmbLineSpacingType.SelectedIndexChanged += CmbLineSpacingType_SelectedIndexChanged;

            // 字体格式事件
            _chkEnableFontFormat.CheckedChanged += ChkEnableFontFormat_CheckedChanged;
            _chkSetChineseFont.CheckedChanged += ChkSetChineseFont_CheckedChanged;
            _chkSetLatinFont.CheckedChanged += ChkSetLatinFont_CheckedChanged;
            _chkSetFontSize.CheckedChanged += ChkSetFontSize_CheckedChanged;
            _chkSetFontColor.CheckedChanged += ChkSetFontColor_CheckedChanged;
            _chkSetUnderline.CheckedChanged += ChkSetUnderline_CheckedChanged;
            _chkSetTextEffects.CheckedChanged += ChkSetTextEffects_CheckedChanged;

            // 颜色选择按钮事件
            _btnFontColor.Click += BtnFontColor_Click;
            _btnUnderlineColor.Click += BtnUnderlineColor_Click;

            // 按钮事件
            _btnOK.Click += BtnOK_Click;
            _btnCancel.Click += BtnCancel_Click;
            _btnApply.Click += BtnApply_Click;
        }

        #endregion

        #region 数据加载和保存

        /// <summary>
        /// 加载规则数据
        /// </summary>
        private void LoadRuleData()
        {
            // 加载规则名称
            _txtRuleName.Text = _currentRule.RuleName;

            // 加载替换范围设置
            _chkIncludeNormalSlides.Checked = _currentRule.ReplacementScope.IncludeNormalSlides;
            _chkIncludeMasterSlides.Checked = _currentRule.ReplacementScope.IncludeMasterSlides;
            _chkIncludeLayoutSlides.Checked = _currentRule.ReplacementScope.IncludeLayoutSlides;

            // 加载匹配条件
            LoadMatchingConditions();

            // 加载段落格式
            LoadParagraphFormat();

            // 加载字体格式
            LoadFontFormat();

            // 更新UI状态
            UpdateControlStates();
        }

        /// <summary>
        /// 加载匹配条件
        /// </summary>
        private void LoadMatchingConditions()
        {
            var conditions = _currentRule.MatchingConditions;

            _chkStartsWith.Checked = conditions.EnableStartsWith;
            _txtStartsWith.Text = conditions.StartsWithText;

            _chkContains.Checked = conditions.EnableContains;
            _listContainsKeywords.Items.Clear();
            foreach (var keyword in conditions.ContainsKeywords)
            {
                _listContainsKeywords.Items.Add(keyword);
            }

            _chkEndsWith.Checked = conditions.EnableEndsWith;
            _txtEndsWith.Text = conditions.EndsWithText;

            _chkRegex.Checked = conditions.EnableRegex;
            _txtRegexPattern.Text = conditions.RegexPattern;

            _chkCharacterCount.Checked = conditions.EnableCharacterCountLimit;
            _numMinCharCount.Value = conditions.MinCharacterCount;
            _numMaxCharCount.Value = conditions.MaxCharacterCount;

            _chkCaseSensitive.Checked = conditions.CaseSensitive;
            _chkWholeWord.Checked = conditions.WholeWord;
        }

        /// <summary>
        /// 加载段落格式
        /// </summary>
        private void LoadParagraphFormat()
        {
            var format = _currentRule.ParagraphFormat;

            _chkEnableParagraphFormat.Checked = format.EnableParagraphFormat;
            _cmbAlignment.SelectedIndex = (int)format.Alignment;

            _chkEnableIndentation.Checked = format.EnableIndentation;
            _numLeftIndent.Value = (decimal)format.LeftIndent;
            _cmbSpecialIndent.SelectedIndex = (int)format.SpecialIndent;
            _numSpecialIndentValue.Value = (decimal)format.SpecialIndentValue;

            _chkEnableSpacing.Checked = format.EnableSpacing;
            _numSpaceBefore.Value = (decimal)format.SpaceBefore;
            _numSpaceAfter.Value = (decimal)format.SpaceAfter;
            _cmbLineSpacingType.SelectedIndex = (int)format.LineSpacingType;
            _numLineSpacingValue.Value = (decimal)format.LineSpacingValue;

            _chkEnableChineseControl.Checked = format.EnableChineseControl;
            _chkChineseCharacterControl.Checked = format.ChineseCharacterControl;
            _chkAllowLatinWordBreak.Checked = format.AllowLatinWordBreak;
            _chkAllowPunctuationOverhang.Checked = format.AllowPunctuationOverhang;

            _cmbTextAlignment.SelectedIndex = (int)format.TextAlignment;
        }

        /// <summary>
        /// 加载字体格式
        /// </summary>
        private void LoadFontFormat()
        {
            var format = _currentRule.FontFormat;

            _chkEnableFontFormat.Checked = format.EnableFontFormat;

            _chkSetChineseFont.Checked = format.SetChineseFont;
            _cmbChineseFontName.Text = format.ChineseFontName;

            _chkSetLatinFont.Checked = format.SetLatinFont;
            _cmbLatinFontName.Text = format.LatinFontName;

            _cmbFontStyle.SelectedIndex = (int)format.FontStyle;

            _chkSetFontSize.Checked = format.SetFontSize;
            _numFontSize.Value = (decimal)format.FontSize;

            _chkSetFontColor.Checked = format.SetFontColor;
            try
            {
                _btnFontColor.BackColor = ColorTranslator.FromHtml(format.FontColor);
            }
            catch
            {
                _btnFontColor.BackColor = Color.Black;
            }

            _chkSetUnderline.Checked = format.SetUnderline;
            _cmbUnderlineType.SelectedIndex = (int)format.UnderlineType;
            try
            {
                _btnUnderlineColor.BackColor = ColorTranslator.FromHtml(format.UnderlineColor);
            }
            catch
            {
                _btnUnderlineColor.BackColor = Color.Black;
            }

            _chkSetTextEffects.Checked = format.SetTextEffects;
            _chkStrikethrough.Checked = format.Strikethrough;
            _chkDoubleStrikethrough.Checked = format.DoubleStrikethrough;
            _chkSuperscript.Checked = format.Superscript;
            _chkSubscript.Checked = format.Subscript;
        }

        /// <summary>
        /// 保存规则数据
        /// </summary>
        private void SaveRuleData()
        {
            // 保存规则名称
            _currentRule.RuleName = _txtRuleName.Text.Trim();

            // 保存替换范围设置
            _currentRule.ReplacementScope.IncludeNormalSlides = _chkIncludeNormalSlides.Checked;
            _currentRule.ReplacementScope.IncludeMasterSlides = _chkIncludeMasterSlides.Checked;
            _currentRule.ReplacementScope.IncludeLayoutSlides = _chkIncludeLayoutSlides.Checked;

            // 保存匹配条件
            SaveMatchingConditions();

            // 保存段落格式
            SaveParagraphFormat();

            // 保存字体格式
            SaveFontFormat();

            // 更新最后修改时间
            _currentRule.LastModifiedTime = DateTime.Now;
        }

        /// <summary>
        /// 保存匹配条件
        /// </summary>
        private void SaveMatchingConditions()
        {
            var conditions = _currentRule.MatchingConditions;

            conditions.EnableStartsWith = _chkStartsWith.Checked;
            conditions.StartsWithText = _txtStartsWith.Text.Trim();

            conditions.EnableContains = _chkContains.Checked;
            conditions.ContainsKeywords.Clear();
            foreach (string item in _listContainsKeywords.Items)
            {
                if (!string.IsNullOrWhiteSpace(item))
                {
                    conditions.ContainsKeywords.Add(item.Trim());
                }
            }

            conditions.EnableEndsWith = _chkEndsWith.Checked;
            conditions.EndsWithText = _txtEndsWith.Text.Trim();

            conditions.EnableRegex = _chkRegex.Checked;
            conditions.RegexPattern = _txtRegexPattern.Text.Trim();

            conditions.EnableCharacterCountLimit = _chkCharacterCount.Checked;
            conditions.MinCharacterCount = (int)_numMinCharCount.Value;
            conditions.MaxCharacterCount = (int)_numMaxCharCount.Value;

            conditions.CaseSensitive = _chkCaseSensitive.Checked;
            conditions.WholeWord = _chkWholeWord.Checked;
        }

        /// <summary>
        /// 保存段落格式
        /// </summary>
        private void SaveParagraphFormat()
        {
            var format = _currentRule.ParagraphFormat;

            format.EnableParagraphFormat = _chkEnableParagraphFormat.Checked;
            format.Alignment = (ParagraphAlignment)_cmbAlignment.SelectedIndex;

            format.EnableIndentation = _chkEnableIndentation.Checked;
            format.LeftIndent = (float)_numLeftIndent.Value;
            format.SpecialIndent = (SpecialIndentType)_cmbSpecialIndent.SelectedIndex;
            format.SpecialIndentValue = (float)_numSpecialIndentValue.Value;

            format.EnableSpacing = _chkEnableSpacing.Checked;
            format.SpaceBefore = (float)_numSpaceBefore.Value;
            format.SpaceAfter = (float)_numSpaceAfter.Value;
            format.LineSpacingType = (LineSpacingType)_cmbLineSpacingType.SelectedIndex;
            format.LineSpacingValue = (float)_numLineSpacingValue.Value;

            format.EnableChineseControl = _chkEnableChineseControl.Checked;
            format.ChineseCharacterControl = _chkChineseCharacterControl.Checked;
            format.AllowLatinWordBreak = _chkAllowLatinWordBreak.Checked;
            format.AllowPunctuationOverhang = _chkAllowPunctuationOverhang.Checked;

            format.TextAlignment = (TextVerticalAlignment)_cmbTextAlignment.SelectedIndex;
        }

        /// <summary>
        /// 保存字体格式
        /// </summary>
        private void SaveFontFormat()
        {
            var format = _currentRule.FontFormat;

            format.EnableFontFormat = _chkEnableFontFormat.Checked;

            format.SetChineseFont = _chkSetChineseFont.Checked;
            format.ChineseFontName = _cmbChineseFontName.Text;

            format.SetLatinFont = _chkSetLatinFont.Checked;
            format.LatinFontName = _cmbLatinFontName.Text;

            format.FontStyle = (FontStyleType)_cmbFontStyle.SelectedIndex;

            format.SetFontSize = _chkSetFontSize.Checked;
            format.FontSize = (float)_numFontSize.Value;

            format.SetFontColor = _chkSetFontColor.Checked;
            format.FontColor = ColorTranslator.ToHtml(_btnFontColor.BackColor);

            format.SetUnderline = _chkSetUnderline.Checked;
            format.UnderlineType = (UnderlineType)_cmbUnderlineType.SelectedIndex;
            format.UnderlineColor = ColorTranslator.ToHtml(_btnUnderlineColor.BackColor);

            format.SetTextEffects = _chkSetTextEffects.Checked;
            format.Strikethrough = _chkStrikethrough.Checked;
            format.DoubleStrikethrough = _chkDoubleStrikethrough.Checked;
            format.Superscript = _chkSuperscript.Checked;
            format.Subscript = _chkSubscript.Checked;
        }

        /// <summary>
        /// 更新控件状态
        /// </summary>
        private void UpdateControlStates()
        {
            // 更新匹配条件控件状态
            _txtStartsWith.Enabled = _chkStartsWith.Checked;
            _listContainsKeywords.Enabled = _chkContains.Checked;
            _txtNewKeyword.Enabled = _chkContains.Checked;
            _btnAddKeyword.Enabled = _chkContains.Checked;
            _btnRemoveKeyword.Enabled = _chkContains.Checked && _listContainsKeywords.SelectedIndex >= 0;
            _txtEndsWith.Enabled = _chkEndsWith.Checked;
            _txtRegexPattern.Enabled = _chkRegex.Checked;
            _numMinCharCount.Enabled = _chkCharacterCount.Checked;
            _numMaxCharCount.Enabled = _chkCharacterCount.Checked;

            // 更新段落格式控件状态
            var paragraphEnabled = _chkEnableParagraphFormat.Checked;
            _cmbAlignment.Enabled = paragraphEnabled;

            var indentEnabled = paragraphEnabled && _chkEnableIndentation.Checked;
            _numLeftIndent.Enabled = indentEnabled;
            _cmbSpecialIndent.Enabled = indentEnabled;
            _numSpecialIndentValue.Enabled = indentEnabled;

            var spacingEnabled = paragraphEnabled && _chkEnableSpacing.Checked;
            _numSpaceBefore.Enabled = spacingEnabled;
            _numSpaceAfter.Enabled = spacingEnabled;
            _cmbLineSpacingType.Enabled = spacingEnabled;
            _numLineSpacingValue.Enabled = spacingEnabled && (_cmbLineSpacingType.SelectedIndex == 3 || _cmbLineSpacingType.SelectedIndex == 4);

            var chineseEnabled = paragraphEnabled && _chkEnableChineseControl.Checked;
            _chkChineseCharacterControl.Enabled = chineseEnabled;
            _chkAllowLatinWordBreak.Enabled = chineseEnabled;
            _chkAllowPunctuationOverhang.Enabled = chineseEnabled;

            _cmbTextAlignment.Enabled = paragraphEnabled;

            // 更新字体格式控件状态
            var fontEnabled = _chkEnableFontFormat.Checked;
            _chkSetChineseFont.Enabled = fontEnabled;
            _cmbChineseFontName.Enabled = fontEnabled && _chkSetChineseFont.Checked;
            _chkSetLatinFont.Enabled = fontEnabled;
            _cmbLatinFontName.Enabled = fontEnabled && _chkSetLatinFont.Checked;
            _cmbFontStyle.Enabled = fontEnabled;
            _chkSetFontSize.Enabled = fontEnabled;
            _numFontSize.Enabled = fontEnabled && _chkSetFontSize.Checked;
            _chkSetFontColor.Enabled = fontEnabled;
            _btnFontColor.Enabled = fontEnabled && _chkSetFontColor.Checked;
            _chkSetUnderline.Enabled = fontEnabled;
            _cmbUnderlineType.Enabled = fontEnabled && _chkSetUnderline.Checked;
            _btnUnderlineColor.Enabled = fontEnabled && _chkSetUnderline.Checked;
            _chkSetTextEffects.Enabled = fontEnabled;

            var textEffectsEnabled = fontEnabled && _chkSetTextEffects.Checked;
            _chkStrikethrough.Enabled = textEffectsEnabled;
            _chkDoubleStrikethrough.Enabled = textEffectsEnabled;
            _chkSuperscript.Enabled = textEffectsEnabled;
            _chkSubscript.Enabled = textEffectsEnabled;
        }

        #endregion

        #region 事件处理器实现

        /// <summary>
        /// 规则名称文本变更事件
        /// </summary>
        private void TxtRuleName_TextChanged(object? sender, EventArgs e)
        {
            // 可以在这里添加规则名称验证逻辑
        }

        /// <summary>
        /// 段落开头匹配复选框变更事件
        /// </summary>
        private void ChkStartsWith_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 段落包含关键词匹配复选框变更事件
        /// </summary>
        private void ChkContains_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 段落结尾匹配复选框变更事件
        /// </summary>
        private void ChkEndsWith_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 正则表达式匹配复选框变更事件
        /// </summary>
        private void ChkRegex_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 字符数限制复选框变更事件
        /// </summary>
        private void ChkCharacterCount_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 添加关键词按钮点击事件
        /// </summary>
        private void BtnAddKeyword_Click(object? sender, EventArgs e)
        {
            var keyword = _txtNewKeyword.Text.Trim();
            if (!string.IsNullOrEmpty(keyword) && !_listContainsKeywords.Items.Contains(keyword))
            {
                _listContainsKeywords.Items.Add(keyword);
                _txtNewKeyword.Clear();
                _txtNewKeyword.Focus();
            }
        }

        /// <summary>
        /// 删除关键词按钮点击事件
        /// </summary>
        private void BtnRemoveKeyword_Click(object? sender, EventArgs e)
        {
            if (_listContainsKeywords.SelectedIndex >= 0)
            {
                _listContainsKeywords.Items.RemoveAt(_listContainsKeywords.SelectedIndex);
                UpdateControlStates();
            }
        }

        /// <summary>
        /// 新关键词文本框按键事件
        /// </summary>
        private void TxtNewKeyword_KeyPress(object? sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                BtnAddKeyword_Click(sender, e);
                e.Handled = true;
            }
        }

        #endregion
    }
}

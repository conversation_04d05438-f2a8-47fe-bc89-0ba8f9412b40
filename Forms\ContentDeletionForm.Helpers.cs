// PPT内容删除设置窗体 - 辅助方法类
// 功能：提供内容删除设置窗体的辅助方法，包括控件创建、数据绑定、设置保存加载等功能
// 作者：PPT批量处理工具开发团队
// 创建时间：2024年
// 最后修改：2024年

using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PPTPiliangChuli.Services;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 内容删除设置窗体 - 辅助方法
    /// </summary>
    public partial class ContentDeletionForm
    {
        /// <summary>
        /// 创建可滚动面板
        /// </summary>
        private Panel CreateScrollablePanel()
        {
            return new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(20, 15, 20, 15),
                BackColor = Color.FromArgb(252, 252, 252)
            };
        }

        /// <summary>
        /// 创建复选框 - 根据文字内容自动计算合适的宽度
        /// </summary>
        private CheckBox CreateCheckBox(string text, int x, int y)
        {
            // 根据文字长度计算合适的宽度，中文字符按12像素计算，英文按8像素计算
            int estimatedWidth = CalculateTextWidth(text, 10F) + 30; // 加30像素用于复选框图标和边距
            int width = Math.Max(estimatedWidth, 120); // 最小宽度120像素
            int height = text.Contains('\n') ? 60 : 40; // 如果包含换行符则增加高度，否则最小40像素
            return CreateCheckBox(text, x, y, width, height);
        }

        /// <summary>
        /// 创建复选框（带自定义尺寸）
        /// </summary>
        private CheckBox CreateCheckBox(string text, int x, int y, int width, int height)
        {
            return new CheckBox
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, Math.Max(height, 40)), // 确保最小高度为40像素
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                UseVisualStyleBackColor = true,
                TextAlign = ContentAlignment.MiddleLeft,
                AutoSize = false
            };
        }

        /// <summary>
        /// 创建标签 - 根据文字内容自动计算合适的宽度
        /// </summary>
        private Label CreateLabel(string text, int x, int y)
        {
            // 根据文字长度计算合适的宽度
            int estimatedWidth = CalculateTextWidth(text, 10F) + 10; // 加10像素边距
            int width = Math.Max(estimatedWidth, 60); // 最小宽度60像素
            int height = text.Contains('\n') ? 50 : 40; // 如果包含换行符则增加高度，否则最小40像素
            return CreateLabel(text, x, y, width, height);
        }

        /// <summary>
        /// 创建标签（带自定义尺寸）
        /// </summary>
        private Label CreateLabel(string text, int x, int y, int width, int height)
        {
            return new Label
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, Math.Max(height, 40)), // 确保最小高度为40像素
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                TextAlign = ContentAlignment.MiddleLeft,
                AutoSize = false
            };
        }

        /// <summary>
        /// 计算文字显示所需的宽度 - 根据字体大小和文字内容估算像素宽度
        /// </summary>
        /// <param name="text">要计算的文字内容</param>
        /// <param name="fontSize">字体大小</param>
        /// <returns>估算的像素宽度</returns>
        private static int CalculateTextWidth(string text, float fontSize)
        {
            if (string.IsNullOrEmpty(text))
                return 0;

            int width = 0;
            foreach (char c in text)
            {
                // 中文字符、全角字符按字体大小的1.2倍计算
                if (c >= 0x4E00 && c <= 0x9FFF || c >= 0xFF00 && c <= 0xFFEF)
                {
                    width += (int)(fontSize * 1.2);
                }
                // 英文字符、数字、标点符号按字体大小的0.6倍计算
                else
                {
                    width += (int)(fontSize * 0.6);
                }
            }
            return width;
        }

        /// <summary>
        /// 创建数值输入框 - 根据数值范围自动调整宽度
        /// </summary>
        private NumericUpDown CreateNumericUpDown(decimal min, decimal max, int x, int y)
        {
            // 根据最大值的位数计算合适的宽度
            int maxDigits = Math.Max(min.ToString().Length, max.ToString().Length);
            int width = Math.Max(maxDigits * 12 + 40, 80); // 每位数字12像素，加40像素用于按钮和边距，最小80像素
            return CreateNumericUpDown(min, max, x, y, width, 40); // 使用40像素高度
        }

        /// <summary>
        /// 创建数值输入框（带自定义尺寸）
        /// </summary>
        private NumericUpDown CreateNumericUpDown(decimal min, decimal max, int x, int y, int width, int height)
        {
            var numericUpDown = new NumericUpDown
            {
                Minimum = min,
                Maximum = max,
                Location = new Point(x, y),
                Size = new Size(width, Math.Max(height, 40)), // 确保最小高度为40像素
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                TextAlign = HorizontalAlignment.Center
            };
            return numericUpDown;
        }

        /// <summary>
        /// 创建下拉框 - 根据选项内容自动调整宽度
        /// </summary>
        private ComboBox CreateComboBox(string[] items, int x, int y)
        {
            // 根据选项中最长的文字计算合适的宽度
            int maxWidth = 80; // 最小宽度80像素
            foreach (string item in items)
            {
                int itemWidth = CalculateTextWidth(item, 10F) + 30; // 加30像素用于下拉箭头和边距
                maxWidth = Math.Max(maxWidth, itemWidth);
            }
            return CreateComboBox(items, x, y, maxWidth, 40); // 使用40像素高度
        }

        /// <summary>
        /// 创建下拉框（带自定义尺寸）
        /// </summary>
        private ComboBox CreateComboBox(string[] items, int x, int y, int width, int height)
        {
            var comboBox = new ComboBox
            {
                Location = new Point(x, y),
                Size = new Size(width, Math.Max(height, 40)), // 确保最小高度为40像素
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                DropDownStyle = ComboBoxStyle.DropDownList,
                DrawMode = DrawMode.OwnerDrawFixed,
                ItemHeight = 25  // 调整项目高度为25，使下拉框更紧凑
            };

            // 设置下拉框文本居中显示
            comboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = comboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    // 修复编译警告：确保字体不为null
                    var font = e.Font ?? comboBox.Font ?? SystemFonts.DefaultFont;
                    TextRenderer.DrawText(e.Graphics, text, font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            comboBox.Items.AddRange(items);
            if (items.Length > 0)
                comboBox.SelectedIndex = 0;
            return comboBox;
        }

        /// <summary>
        /// 创建文本框
        /// </summary>
        private TextBox CreateTextBox(int x, int y, int width, int height, bool multiline = false)
        {
            var textBox = new TextBox
            {
                Location = new Point(x, y),
                Size = new Size(width, height),
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                Multiline = multiline,
                ScrollBars = multiline ? ScrollBars.Vertical : ScrollBars.None,
                TextAlign = HorizontalAlignment.Center
            };

            // 为多行文本框添加内边距和调整对齐方式
            if (multiline)
            {
                textBox.Padding = new Padding(10, 10, 10, 10);
                textBox.TextAlign = HorizontalAlignment.Left; // 多行文本框左对齐更合适
                textBox.AcceptsReturn = true; // 接受回车键
                textBox.AcceptsTab = true; // 接受Tab键
                textBox.WordWrap = true; // 自动换行

                // 添加按键事件处理，防止按键被窗体默认按钮捕获
                textBox.KeyDown += MultilineTextBox_KeyDown;
                textBox.KeyPress += MultilineTextBox_KeyPress;
            }

            return textBox;
        }

        /// <summary>
        /// 多行文本框按键按下事件处理
        /// </summary>
        private void MultilineTextBox_KeyDown(object? sender, KeyEventArgs e)
        {
            if (sender is TextBox textBox && textBox.Multiline)
            {
                // 阻止所有按键被窗体处理，让文本框优先处理
                e.Handled = false;
                e.SuppressKeyPress = false;

                // 特别处理一些可能被窗体捕获的按键
                switch (e.KeyCode)
                {
                    case Keys.Enter:
                        // 在多行文本框中插入换行
                        if (!e.Control && !e.Alt && !e.Shift)
                        {
                            textBox.SelectedText = Environment.NewLine;
                            e.Handled = true;
                            e.SuppressKeyPress = true;
                        }
                        break;
                    case Keys.Space:
                        // 确保空格键正常工作
                        e.Handled = false;
                        break;
                    case Keys.Tab:
                        // 在文本框中插入Tab字符
                        if (!e.Control && !e.Alt)
                        {
                            textBox.SelectedText = "\t";
                            e.Handled = true;
                            e.SuppressKeyPress = true;
                        }
                        break;
                }
            }
        }

        /// <summary>
        /// 多行文本框按键字符事件处理
        /// </summary>
        private void MultilineTextBox_KeyPress(object? sender, KeyPressEventArgs e)
        {
            if (sender is TextBox textBox && textBox.Multiline)
            {
                // 对于普通字符输入，确保不被拦截
                if (char.IsControl(e.KeyChar))
                {
                    // 对于控制字符，让文本框自己处理
                    e.Handled = false;
                }
                else
                {
                    // 对于普通字符，确保能正常输入
                    e.Handled = false;
                }
            }
        }

        /// <summary>
        /// 创建按钮 - 根据文字内容自动计算合适的尺寸
        /// </summary>
        private Button CreateButton(string text, int x, int y)
        {
            // 根据文字长度计算合适的宽度
            int estimatedWidth = CalculateTextWidth(text, 10F) + 20; // 加20像素边距
            int width = Math.Max(estimatedWidth, 80); // 最小宽度80像素
            int height = text.Contains('\n') ? 50 : 40; // 如果包含换行符则增加高度，否则最小40像素
            return CreateButton(text, x, y, width, height);
        }

        /// <summary>
        /// 创建按钮（带自定义尺寸）
        /// </summary>
        private Button CreateButton(string text, int x, int y, int width, int height)
        {
            return new Button
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, Math.Max(height, 40)), // 确保最小高度为40像素
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                UseVisualStyleBackColor = true,
                TextAlign = ContentAlignment.MiddleCenter
            };
        }

        /// <summary>
        /// 创建分组框
        /// </summary>
        private GroupBox CreateGroupBox(string text, int x, int y, int width, int height)
        {
            return new GroupBox
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, height),
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                Padding = new Padding(20, 35, 20, 20)
            };
        }

        /// <summary>
        /// 设置复选框值
        /// </summary>
        private void SetCheckBoxValue(Panel panel, string name, bool value)
        {
            var control = FindControlByName(panel, name) as CheckBox;
            if (control != null)
                control.Checked = value;
        }

        /// <summary>
        /// 设置数值输入框值
        /// </summary>
        private void SetNumericUpDownValue(Panel panel, string name, decimal value)
        {
            var control = FindControlByName(panel, name) as NumericUpDown;
            if (control != null)
            {
                if (value >= control.Minimum && value <= control.Maximum)
                    control.Value = value;
            }
        }

        /// <summary>
        /// 设置下拉框值
        /// </summary>
        private void SetComboBoxValue(Panel panel, string name, string value)
        {
            var control = FindControlByName(panel, name) as ComboBox;
            if (control != null)
            {
                var index = control.Items.IndexOf(value);
                if (index >= 0)
                    control.SelectedIndex = index;
            }
        }

        /// <summary>
        /// 设置文本框值
        /// </summary>
        private void SetTextBoxValue(Panel panel, string name, string value)
        {
            var control = FindControlByName(panel, name) as TextBox;
            if (control != null)
                control.Text = value ?? string.Empty;
        }

        /// <summary>
        /// 获取复选框值
        /// </summary>
        private bool GetCheckBoxValue(Panel panel, string name)
        {
            var control = FindControlByName(panel, name) as CheckBox;
            return control?.Checked ?? false;
        }

        /// <summary>
        /// 获取数值输入框值
        /// </summary>
        private decimal GetNumericUpDownValue(Panel panel, string name)
        {
            var control = FindControlByName(panel, name) as NumericUpDown;
            return control?.Value ?? 0;
        }

        /// <summary>
        /// 获取下拉框值
        /// </summary>
        private string GetComboBoxValue(Panel panel, string name)
        {
            var control = FindControlByName(panel, name) as ComboBox;
            return control?.SelectedItem?.ToString() ?? string.Empty;
        }

        /// <summary>
        /// 获取文本框值
        /// </summary>
        private string GetTextBoxValue(Panel panel, string name)
        {
            var control = FindControlByName(panel, name) as TextBox;
            return control?.Text ?? string.Empty;
        }

        /// <summary>
        /// 根据名称查找控件
        /// </summary>
        private Control? FindControlByName(Control parent, string name)
        {
            if (parent.Name == name)
                return parent;

            foreach (Control child in parent.Controls)
            {
                var found = FindControlByName(child, name);
                if (found != null)
                    return found;
            }

            return null;
        }

        /// <summary>
        /// 获取当前设置
        /// </summary>
        public Models.ContentDeletionSettings GetCurrentSettings()
        {
            try
            {
                SaveUIToSettings();
                return _currentSettings;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"获取当前设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new Models.ContentDeletionSettings();
            }
        }

        /// <summary>
        /// 保存界面设置到配置
        /// </summary>
        private void SaveUIToSettings()
        {
            try
            {
                // 保存删除文档设置
                SaveDocumentDeletionSettings();

                // 保存内容删除设置
                SaveContentRemovalSettings();

                // 保存文本删除设置
                SaveTextDeletionSettings();

                // 保存图片删除设置
                SaveImageDeletionSettings();

                // 保存表格删除设置
                SaveTableDeletionSettings();

                // 保存图表删除设置
                SaveChartDeletionSettings();

                // 保存音频视频删除设置
                SaveMediaDeletionSettings();

                // 保存联系方式删除设置
                SaveContactDeletionSettings();

                // 保存动画删除设置
                SaveAnimationDeletionSettings();

                // 保存备注删除设置
                SaveNotesDeletionSettings();

                // 保存格式删除设置
                SaveFormatDeletionSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存界面设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存删除文档设置
        /// </summary>
        private void SaveDocumentDeletionSettings()
        {
            var settings = _currentSettings.DocumentDeletion;
            var panel = _tabPanels["DocumentDeletion"];

            // 总开关
            settings.EnableDocumentDeletion = GetCheckBoxValue(panel, "chkDocumentDeletionMaster");

            // 删除范围设置
            settings.DeletionScope = GetDeletionScope(panel, "DocumentDeletion");

            // 文件名长度检查
            settings.EnableFileNameLengthCheck = GetCheckBoxValue(panel, "chkFileNameLengthCheck");
            settings.FileNameMinLength = (int)GetNumericUpDownValue(panel, "numFileNameMinLength");
            settings.FileNameMaxLength = (int)GetNumericUpDownValue(panel, "numFileNameMaxLength");

            // 文件大小检查
            settings.EnableFileSizeCheck = GetCheckBoxValue(panel, "chkFileSizeCheck");
            settings.FileMinSize = (long)GetNumericUpDownValue(panel, "numFileSizeMin");
            settings.FileMaxSize = (long)GetNumericUpDownValue(panel, "numFileSizeMax");
            settings.FileMinSizeUnit = GetComboBoxValue(panel, "cmbFileSizeMinUnit");
            settings.FileMaxSizeUnit = GetComboBoxValue(panel, "cmbFileSizeMaxUnit");

            // 内容字符数检查
            settings.EnableContentCharCountCheck = GetCheckBoxValue(panel, "chkContentCharCountCheck");
            settings.ContentMinCharCount = (int)GetNumericUpDownValue(panel, "numContentCharMin");
            settings.ContentMaxCharCount = (int)GetNumericUpDownValue(panel, "numContentCharMax");

            // 页数检查
            settings.EnablePageCountCheck = GetCheckBoxValue(panel, "chkPageCountCheck");
            settings.MinPageCount = (int)GetNumericUpDownValue(panel, "numPageMin");
            settings.MaxPageCount = (int)GetNumericUpDownValue(panel, "numPageMax");

            // 非法词检查
            settings.EnableFileNameIllegalWordsCheck = GetCheckBoxValue(panel, "chkFileNameIllegalCheck");
            // 文件名非法词已通过弹窗直接保存到settings中，无需从界面获取

            settings.EnableContentIllegalWordsCheck = GetCheckBoxValue(panel, "chkContentIllegalCheck");
            // 内容非法词已通过弹窗直接保存到settings中，无需从界面获取
        }

        /// <summary>
        /// 保存内容删除设置
        /// </summary>
        private void SaveContentRemovalSettings()
        {
            var settings = _currentSettings.ContentRemoval;
            var panel = _tabPanels["ContentRemoval"];

            // 总开关
            settings.EnableContentRemoval = GetCheckBoxValue(panel, "chkContentRemovalMaster");

            // 删除范围设置
            settings.DeletionScope = GetDeletionScope(panel, "ContentRemoval");

            settings.DeleteBlankSlides = GetCheckBoxValue(panel, "chkDeleteBlankSlides");
            settings.DeleteSlidesWithoutText = GetCheckBoxValue(panel, "chkDeleteSlidesWithoutText");
            settings.DeleteFirstSlide = GetCheckBoxValue(panel, "chkDeleteFirstSlide");
            settings.DeleteLastSlide = GetCheckBoxValue(panel, "chkDeleteLastSlide");
            settings.EnableSlideRangeDeletion = GetCheckBoxValue(panel, "chkDeleteSlideRange");
            settings.SlideRangeStart = (int)GetNumericUpDownValue(panel, "numSlideRangeStart");
            settings.SlideRangeEnd = (int)GetNumericUpDownValue(panel, "numSlideRangeEnd");
            settings.EnableKeywordSlidesDeletion = GetCheckBoxValue(panel, "chkDeleteKeywordSlides");
            var slideKeywordsText = GetTextBoxValue(panel, "txtSlideKeywords");
            settings.SlideKeywords = slideKeywordsText.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(k => k.Trim()).ToList();

            settings.EnableSpecificImageSlidesDeletion = GetCheckBoxValue(panel, "chkDeleteSpecificImageSlides");
            settings.SpecificImageNames = SaveSpecificImagesList(panel);

            settings.DeleteBlankParagraphs = GetCheckBoxValue(panel, "chkDeleteBlankParagraphs");
            settings.DeleteBlankLines = GetCheckBoxValue(panel, "chkDeleteBlankLines");
        }

        /// <summary>
        /// 保存文本删除设置
        /// </summary>
        private void SaveTextDeletionSettings()
        {
            var settings = _currentSettings.TextDeletion;
            var panel = _tabPanels["TextDeletion"];

            // 总开关
            settings.EnableTextDeletion = GetCheckBoxValue(panel, "chkTextDeletionMaster");

            // 删除范围设置
            settings.DeletionScope = GetDeletionScope(panel, "TextDeletion");

            // 新增的文本删除功能
            settings.DeleteAllText = GetCheckBoxValue(panel, "chkDeleteAllText");
            settings.DeleteTextBoxes = GetCheckBoxValue(panel, "chkDeleteTextBoxes");
            settings.DeleteTitles = GetCheckBoxValue(panel, "chkDeleteTitles");
            settings.DeleteBulletPoints = GetCheckBoxValue(panel, "chkDeleteBulletPoints");

            // 删除特定文本
            settings.DeleteSpecificText = GetCheckBoxValue(panel, "chkDeleteSpecificText");
            var specificTextContent = GetTextBoxValue(panel, "txtSpecificTextContent");
            settings.SpecificTextContent = specificTextContent.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries).ToList();

            // 删除文本范围
            settings.DeleteTextRange = GetCheckBoxValue(panel, "chkDeleteTextRange");
            settings.TextSlideRangeStart = (int)GetNumericUpDownValue(panel, "numTextRangeStart");
            settings.TextSlideRangeEnd = (int)GetNumericUpDownValue(panel, "numTextRangeEnd");

            // 保留原有的功能（向后兼容）
            settings.DeleteParagraphsWithText = GetCheckBoxValue(panel, "chkDeleteParagraphsWithText");
            var paragraphKeywordsText = GetTextBoxValue(panel, "txtParagraphKeywords");
            settings.ParagraphKeywords = paragraphKeywordsText.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries).ToList();

            settings.DeleteTextBoxesWithText = GetCheckBoxValue(panel, "chkDeleteTextBoxesWithText");
            var textBoxKeywordsText = GetTextBoxValue(panel, "txtTextBoxKeywords");
            settings.TextBoxKeywords = textBoxKeywordsText.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries).ToList();

            settings.DeleteTablesWithText = GetCheckBoxValue(panel, "chkDeleteTablesWithText");
            var tableKeywordsText = GetTextBoxValue(panel, "txtTableKeywords");
            settings.TableKeywords = tableKeywordsText.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries).ToList();
        }

        /// <summary>
        /// 保存图片删除设置
        /// </summary>
        private void SaveImageDeletionSettings()
        {
            var settings = _currentSettings.ImageDeletion;
            var panel = _tabPanels["ImageDeletion"];

            // 总开关
            settings.EnableImageDeletion = GetCheckBoxValue(panel, "chkImageDeletionMaster");

            // 删除范围设置
            settings.DeletionScope = GetDeletionScope(panel, "ImageDeletion");

            settings.DeleteAllImages = GetCheckBoxValue(panel, "chkDeleteAllImages");
            settings.EnableSpecificImageDeletion = GetCheckBoxValue(panel, "chkDeleteSpecificImages");
            var specificImagesText = GetTextBoxValue(panel, "txtSpecificImageNames");
            settings.SpecificImageNames = specificImagesText.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(k => k.Trim()).ToList();
            settings.DeleteLastSlideImages = GetCheckBoxValue(panel, "chkDeleteLastSlideImages");
            settings.DeleteBackgroundImages = GetCheckBoxValue(panel, "chkDeleteBackgroundImages");
            settings.EnableSlideRangeImageDeletion = GetCheckBoxValue(panel, "chkDeleteImageRange");
            settings.ImageSlideRangeStart = (int)GetNumericUpDownValue(panel, "numImageRangeStart");
            settings.ImageSlideRangeEnd = (int)GetNumericUpDownValue(panel, "numImageRangeEnd");
            settings.EnablePositionImageDeletion = GetCheckBoxValue(panel, "chkDeletePositionImages");

            // 保存当前编辑区域的值（保持向后兼容性）
            settings.PositionXPercent = (float)GetNumericUpDownValue(panel, "numPositionXPercent");
            settings.PositionYPercent = (float)GetNumericUpDownValue(panel, "numPositionYPercent");
            settings.PositionWidthPercent = (float)GetNumericUpDownValue(panel, "numPositionWidthPercent");
            settings.PositionHeightPercent = (float)GetNumericUpDownValue(panel, "numPositionHeightPercent");

            // 位置区域列表已经在内存中直接管理，无需从界面获取
            // settings.PositionRegions 已经通过区域管理功能直接更新
        }

        /// <summary>
        /// 保存表格删除设置
        /// </summary>
        private void SaveTableDeletionSettings()
        {
            var settings = _currentSettings.TableDeletion;
            var panel = _tabPanels["TableDeletion"];

            // 总开关
            settings.EnableTableDeletion = GetCheckBoxValue(panel, "chkTableDeletionMaster");

            // 删除范围设置
            settings.DeletionScope = GetDeletionScope(panel, "TableDeletion");

            settings.DeleteAllTables = GetCheckBoxValue(panel, "chkDeleteAllTables");
            settings.DeleteLastSlideTables = GetCheckBoxValue(panel, "chkDeleteLastSlideTables");
            settings.EnableSlideRangeTableDeletion = GetCheckBoxValue(panel, "chkDeleteTableRange");
            settings.TableSlideRangeStart = (int)GetNumericUpDownValue(panel, "numTableRangeStart");
            settings.TableSlideRangeEnd = (int)GetNumericUpDownValue(panel, "numTableRangeEnd");
        }

        /// <summary>
        /// 保存图表删除设置
        /// </summary>
        private void SaveChartDeletionSettings()
        {
            var settings = _currentSettings.ChartDeletion;
            var panel = _tabPanels["ChartDeletion"];

            // 总开关
            settings.EnableChartDeletion = GetCheckBoxValue(panel, "chkChartDeletionMaster");

            // 删除范围设置
            settings.DeletionScope = GetDeletionScope(panel, "ChartDeletion");

            settings.DeleteAllCharts = GetCheckBoxValue(panel, "chkDeleteAllCharts");
            settings.DeleteLastSlideCharts = GetCheckBoxValue(panel, "chkDeleteLastSlideCharts");
            settings.EnableSlideRangeChartDeletion = GetCheckBoxValue(panel, "chkDeleteChartRange");
            settings.ChartSlideRangeStart = (int)GetNumericUpDownValue(panel, "numChartRangeStart");
            settings.ChartSlideRangeEnd = (int)GetNumericUpDownValue(panel, "numChartRangeEnd");
            settings.DeleteChartsWithText = GetCheckBoxValue(panel, "chkDeleteChartsWithText");
            // ChartKeywords 已经在编辑对话框中直接更新到设置对象中
        }

        /// <summary>
        /// 保存音频视频删除设置
        /// </summary>
        private void SaveMediaDeletionSettings()
        {
            var settings = _currentSettings.MediaDeletion;
            var panel = _tabPanels["MediaDeletion"];

            // 总开关
            settings.EnableMediaDeletion = GetCheckBoxValue(panel, "chkMediaDeletionMaster");

            // 删除范围设置
            settings.DeletionScope = GetDeletionScope(panel, "MediaDeletion");

            settings.DeleteAllAudio = GetCheckBoxValue(panel, "chkDeleteAllAudio");
            settings.DeleteLastSlideAudio = GetCheckBoxValue(panel, "chkDeleteLastSlideAudio");
            settings.EnableSlideRangeAudioDeletion = GetCheckBoxValue(panel, "chkDeleteAudioRange");
            settings.AudioSlideRangeStart = (int)GetNumericUpDownValue(panel, "numAudioRangeStart");
            settings.AudioSlideRangeEnd = (int)GetNumericUpDownValue(panel, "numAudioRangeEnd");
            settings.DeleteAllVideo = GetCheckBoxValue(panel, "chkDeleteAllVideo");
            settings.DeleteLastSlideVideo = GetCheckBoxValue(panel, "chkDeleteLastSlideVideo");
            settings.EnableSlideRangeVideoDeletion = GetCheckBoxValue(panel, "chkDeleteVideoRange");
            settings.VideoSlideRangeStart = (int)GetNumericUpDownValue(panel, "numVideoRangeStart");
            settings.VideoSlideRangeEnd = (int)GetNumericUpDownValue(panel, "numVideoRangeEnd");
        }

        /// <summary>
        /// 保存联系方式删除设置
        /// </summary>
        private void SaveContactDeletionSettings()
        {
            var settings = _currentSettings.ContactDeletion;
            var panel = _tabPanels["ContactDeletion"];

            // 总开关
            settings.EnableContactDeletion = GetCheckBoxValue(panel, "chkContactDeletionMaster");

            // 删除范围设置
            settings.DeletionScope = GetDeletionScope(panel, "ContactDeletion");

            settings.DeletePhoneNumbers = GetCheckBoxValue(panel, "chkDeletePhoneNumbers");
            settings.DeleteLandlineNumbers = GetCheckBoxValue(panel, "chkDeleteLandlineNumbers");
            settings.DeleteEmailAddresses = GetCheckBoxValue(panel, "chkDeleteEmailAddresses");
            settings.DeleteWebsites = GetCheckBoxValue(panel, "chkDeleteWebsites");
            settings.DeleteHyperlinks = GetCheckBoxValue(panel, "chkDeleteHyperlinks");
        }

        /// <summary>
        /// 保存动画删除设置
        /// </summary>
        private void SaveAnimationDeletionSettings()
        {
            var settings = _currentSettings.AnimationDeletion;
            var panel = _tabPanels["AnimationDeletion"];

            // 总开关
            settings.EnableAnimationDeletion = GetCheckBoxValue(panel, "chkAnimationDeletionMaster");

            // 删除范围设置
            settings.DeletionScope = GetDeletionScope(panel, "AnimationDeletion");

            settings.DeleteAllAnimations = GetCheckBoxValue(panel, "chkDeleteAllAnimations");
            settings.DeleteLastSlideAnimations = GetCheckBoxValue(panel, "chkDeleteLastSlideAnimations");
            settings.EnableSlideRangeAnimationDeletion = GetCheckBoxValue(panel, "chkDeleteAnimationRange");
            settings.AnimationSlideRangeStart = (int)GetNumericUpDownValue(panel, "numAnimationRangeStart");
            settings.AnimationSlideRangeEnd = (int)GetNumericUpDownValue(panel, "numAnimationRangeEnd");
            settings.DeleteAllTransitions = GetCheckBoxValue(panel, "chkDeleteAllTransitions");
            settings.DeleteLastSlideTransitions = GetCheckBoxValue(panel, "chkDeleteLastSlideTransitions");
            settings.EnableSlideRangeTransitionDeletion = GetCheckBoxValue(panel, "chkDeleteTransitionRange");
            settings.TransitionSlideRangeStart = (int)GetNumericUpDownValue(panel, "numTransitionRangeStart");
            settings.TransitionSlideRangeEnd = (int)GetNumericUpDownValue(panel, "numTransitionRangeEnd");
        }

        /// <summary>
        /// 保存备注删除设置
        /// </summary>
        private void SaveNotesDeletionSettings()
        {
            var settings = _currentSettings.NotesDeletion;
            var panel = _tabPanels["NotesDeletion"];

            // 总开关
            settings.EnableNotesDeletion = GetCheckBoxValue(panel, "chkNotesDeletionMaster");

            // 删除范围设置
            settings.DeletionScope = GetDeletionScope(panel, "NotesDeletion");

            settings.DeleteSlideNotes = GetCheckBoxValue(panel, "chkDeleteSlideNotes");
            settings.ClearNotesContent = GetCheckBoxValue(panel, "chkClearNotesContent");
        }

        /// <summary>
        /// 保存格式删除设置
        /// </summary>
        private void SaveFormatDeletionSettings()
        {
            var settings = _currentSettings.FormatDeletion;
            var panel = _tabPanels["FormatDeletion"];

            // 总开关
            settings.EnableFormatDeletion = GetCheckBoxValue(panel, "chkFormatDeletionMaster");

            // 删除范围设置
            settings.DeletionScope = GetDeletionScope(panel, "FormatDeletion");

            // 文本格式删除选项
            settings.DeleteFontFormatting = GetCheckBoxValue(panel, "chkDeleteFontFormatting");
            settings.DeleteParagraphFormatting = GetCheckBoxValue(panel, "chkDeleteParagraphFormatting");
            settings.DeleteListFormatting = GetCheckBoxValue(panel, "chkDeleteListFormatting");

            // 对象格式删除选项
            settings.DeleteTableFormatting = GetCheckBoxValue(panel, "chkDeleteTableFormatting");
            settings.DeleteShapeFormatting = GetCheckBoxValue(panel, "chkDeleteShapeFormatting");
            settings.DeleteImageFormatting = GetCheckBoxValue(panel, "chkDeleteImageFormatting");
            settings.DeleteChartFormatting = GetCheckBoxValue(panel, "chkDeleteChartFormatting");

            // 背景格式删除选项
            settings.DeleteBackgroundFormatting = GetCheckBoxValue(panel, "chkDeleteBackgroundFormatting");
        }

        /// <summary>
        /// 保存设置到配置文件
        /// </summary>
        private void SaveSettingsToConfig()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();
                config.ContentDeletionSettings = _currentSettings;
                ConfigService.Instance.UpdateConfig(config);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存配置文件失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 获取删除范围设置 - 根据复选框状态获取删除范围枚举值
        /// </summary>
        /// <param name="panel">包含删除范围控件的面板</param>
        /// <param name="controlNamePrefix">控件名称前缀</param>
        /// <returns>删除范围枚举值</returns>
        private DeletionScope GetDeletionScope(Panel panel, string controlNamePrefix)
        {
            var scope = (DeletionScope)0;

            var chkNormalSlides = FindControlByName(panel, $"chk{controlNamePrefix}NormalSlides") as CheckBox;
            var chkMasters = FindControlByName(panel, $"chk{controlNamePrefix}Masters") as CheckBox;
            var chkLayoutSlides = FindControlByName(panel, $"chk{controlNamePrefix}LayoutSlides") as CheckBox;

            if (chkNormalSlides?.Checked == true)
                scope |= DeletionScope.NormalSlides;

            if (chkMasters?.Checked == true)
                scope |= DeletionScope.Masters;

            if (chkLayoutSlides?.Checked == true)
                scope |= DeletionScope.LayoutSlides;

            // 如果没有选择任何范围，默认为普通幻灯片页
            if (scope == 0)
                scope = DeletionScope.NormalSlides;

            return scope;
        }

        /// <summary>
        /// 设置删除范围到界面 - 根据删除范围枚举值设置复选框状态
        /// </summary>
        /// <param name="panel">包含删除范围控件的面板</param>
        /// <param name="controlNamePrefix">控件名称前缀</param>
        /// <param name="scope">删除范围枚举值</param>
        private void SetDeletionScope(Panel panel, string controlNamePrefix, DeletionScope scope)
        {
            var chkNormalSlides = FindControlByName(panel, $"chk{controlNamePrefix}NormalSlides") as CheckBox;
            var chkMasters = FindControlByName(panel, $"chk{controlNamePrefix}Masters") as CheckBox;
            var chkLayoutSlides = FindControlByName(panel, $"chk{controlNamePrefix}LayoutSlides") as CheckBox;

            if (chkNormalSlides != null)
                chkNormalSlides.Checked = scope.HasFlag(DeletionScope.NormalSlides);

            if (chkMasters != null)
                chkMasters.Checked = scope.HasFlag(DeletionScope.Masters);

            if (chkLayoutSlides != null)
                chkLayoutSlides.Checked = scope.HasFlag(DeletionScope.LayoutSlides);
        }

        /// <summary>
        /// 根据删除范围获取需要处理的幻灯片集合 - 符合Aspose.Slides API规范
        /// 返回分类的幻灯片集合，便于不同类型的删除操作
        /// </summary>
        /// <param name="presentation">演示文稿对象</param>
        /// <param name="scope">删除范围</param>
        /// <returns>分类的幻灯片集合</returns>
        public static SlideScopeResult GetSlidesForDeletionScope(Aspose.Slides.IPresentation presentation, DeletionScope scope)
        {
            var result = new SlideScopeResult();

            // 处理普通幻灯片页
            if (scope.HasFlag(DeletionScope.NormalSlides))
            {
                result.NormalSlides.AddRange(presentation.Slides);
            }

            // 处理母版
            if (scope.HasFlag(DeletionScope.Masters))
            {
                result.Masters.AddRange(presentation.Masters);
            }

            // 处理版式页
            if (scope.HasFlag(DeletionScope.LayoutSlides))
            {
                foreach (var master in presentation.Masters)
                {
                    result.LayoutSlides.AddRange(master.LayoutSlides);
                }
            }

            return result;
        }

        /// <summary>
        /// 幻灯片范围结果 - 包含不同类型的幻灯片集合
        /// </summary>
        public class SlideScopeResult
        {
            /// <summary>
            /// 普通幻灯片集合
            /// </summary>
            public List<Aspose.Slides.ISlide> NormalSlides { get; set; } = new List<Aspose.Slides.ISlide>();

            /// <summary>
            /// 母版集合
            /// </summary>
            public List<Aspose.Slides.IMasterSlide> Masters { get; set; } = new List<Aspose.Slides.IMasterSlide>();

            /// <summary>
            /// 版式页集合
            /// </summary>
            public List<Aspose.Slides.ILayoutSlide> LayoutSlides { get; set; } = new List<Aspose.Slides.ILayoutSlide>();

            /// <summary>
            /// 是否有任何幻灯片需要处理
            /// </summary>
            public bool HasAnySlides => NormalSlides.Count > 0 || Masters.Count > 0 || LayoutSlides.Count > 0;
        }

        /// <summary>
        /// 将文件大小转换为字节
        /// </summary>
        /// <param name="size">大小数值</param>
        /// <param name="unit">单位（B、KB、MB、GB）</param>
        /// <returns>字节数</returns>
        private long ConvertToBytes(decimal size, string unit)
        {
            return unit?.ToUpper() switch
            {
                "B" => (long)size,
                "KB" => (long)(size * 1024),
                "MB" => (long)(size * 1024 * 1024),
                "GB" => (long)(size * 1024 * 1024 * 1024),
                _ => (long)size // 默认按字节处理
            };
        }
    }
}

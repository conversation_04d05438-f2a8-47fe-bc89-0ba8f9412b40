/// <summary>
/// PPT页脚设置窗体 - 用于配置PowerPoint演示文稿的页眉页脚设置
/// 支持删除设置、演示文稿级别设置、普通幻灯片设置、母版幻灯片设置、布局幻灯片设置、备注幻灯片设置
/// 提供页脚文本、页码格式、日期时间格式等全面的页眉页脚配置功能
/// </summary>

using System;
using System.Drawing;
using System.Windows.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// PPT页脚设置窗体
    /// </summary>
    public partial class HeaderFooterSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// PPT页脚设置
        /// </summary>
        public HeaderFooterSettings Settings { get; private set; } = new HeaderFooterSettings();

        /// <summary>
        /// 是否已修改
        /// </summary>
        // 移除未使用的字段以消除警告

        #region 控件字段
        private TabControl? _tabControl;
        private Button? _btnOK, _btnCancel, _btnReset;

        // 标签页Panel容器（用于滚动）
        private Panel? _panelDeletion, _panelPresentation, _panelSlide, _panelMaster, _panelLayout, _panelNotes;

        // 删除设置页面控件
        private CheckBox? _chkDeleteFooters, _chkDeleteSlideNumbers, _chkDeleteDateTimes;
        private ComboBox? _cmbDeletionScope;

        // 演示文稿级别设置页面控件
        private CheckBox? _chkPresentationEnabled;
        private TextBox? _txtPresentationFooter, _txtPresentationDateTime;
        private CheckBox? _chkPresentationShowFooter, _chkPresentationShowSlideNumber, _chkPresentationShowDateTime;
        private ComboBox? _cmbPresentationDateTimeFormat;
        private CheckBox? _chkPresentationAutoUpdateDateTime;

        // 普通幻灯片设置页面控件
        private CheckBox? _chkSlideEnabled;
        private GroupBox? _grpSlideFooter, _grpSlideNumber, _grpSlideDateTime;
        private TextBox? _txtSlideFooter, _txtSlideRange, _txtSlideDateTimeCustom;
        private CheckBox? _chkSlideFooterEnabled, _chkSlideNumberEnabled, _chkSlideDateTimeEnabled;
        private CheckBox? _chkSlideFooterVisible, _chkSlideNumberVisible, _chkSlideDateTimeVisible;
        private ComboBox? _cmbSlideNumberFormat, _cmbSlideDateTimeFormat, _cmbSlideApplyScope;
        private NumericUpDown? _nudSlideNumberStart;
        private CheckBox? _chkSlideDateTimeAutoUpdate, _chkSlideDateTimeUseCustom;
        private ListBox? _lstSelectedSlides;

        // 母版幻灯片设置页面控件
        private CheckBox? _chkMasterEnabled;
        private GroupBox? _grpMasterFooter, _grpMasterNumber, _grpMasterDateTime;
        private TextBox? _txtMasterFooter, _txtMasterDateTimeCustom;
        private CheckBox? _chkMasterFooterEnabled, _chkMasterNumberEnabled, _chkMasterDateTimeEnabled;
        private CheckBox? _chkMasterFooterVisible, _chkMasterNumberVisible, _chkMasterDateTimeVisible;
        private CheckBox? _chkMasterApplyToChildren, _chkMasterDateTimeAutoUpdate, _chkMasterDateTimeUseCustom;
        private ComboBox? _cmbMasterNumberFormat, _cmbMasterDateTimeFormat;
        private NumericUpDown? _nudMasterNumberStart;

        // 布局幻灯片设置页面控件
        private CheckBox? _chkLayoutEnabled;
        private GroupBox? _grpLayoutFooter, _grpLayoutNumber, _grpLayoutDateTime;
        private CheckBox? _chkLayoutApplyToAll;
        private ListBox? _lstLayoutSlides;
        private TextBox? _txtLayoutFooter, _txtLayoutDateTimeCustom;
        private CheckBox? _chkLayoutFooterEnabled, _chkLayoutNumberEnabled, _chkLayoutDateTimeEnabled;
        private CheckBox? _chkLayoutFooterVisible, _chkLayoutNumberVisible, _chkLayoutDateTimeVisible;
        private CheckBox? _chkLayoutDateTimeAutoUpdate, _chkLayoutDateTimeUseCustom;
        private ComboBox? _cmbLayoutNumberFormat, _cmbLayoutDateTimeFormat;
        private NumericUpDown? _nudLayoutNumberStart;

        // 备注幻灯片设置页面控件
        private CheckBox? _chkNotesEnabled;
        private TabControl? _tabNotesSettings;
        private GroupBox? _grpNotesMaster, _grpNotesSlide;

        // 备注母版控件
        private TextBox? _txtNotesMasterFooter, _txtNotesMasterDateTimeCustom;
        private CheckBox? _chkNotesMasterFooterEnabled, _chkNotesMasterNumberEnabled, _chkNotesMasterDateTimeEnabled;
        private CheckBox? _chkNotesMasterFooterVisible, _chkNotesMasterNumberVisible, _chkNotesMasterDateTimeVisible;
        private CheckBox? _chkNotesMasterApplyToChildren, _chkNotesMasterDateTimeAutoUpdate, _chkNotesMasterDateTimeUseCustom;
        private ComboBox? _cmbNotesMasterNumberFormat, _cmbNotesMasterDateTimeFormat;
        private NumericUpDown? _nudNotesMasterNumberStart;

        // 备注幻灯片控件
        private TextBox? _txtNotesSlideFooter, _txtNotesSlideRange, _txtNotesSlideeDateTimeCustom;
        private CheckBox? _chkNotesSlideFooterEnabled, _chkNotesSlideNumberEnabled, _chkNotesSlideeDateTimeEnabled;
        private CheckBox? _chkNotesSlideFooterVisible, _chkNotesSlideNumberVisible, _chkNotesSlideeDateTimeVisible;
        private CheckBox? _chkNotesSlideeDateTimeAutoUpdate, _chkNotesSlideeDateTimeUseCustom;
        private ComboBox? _cmbNotesSlideNumberFormat, _cmbNotesSlideeDateTimeFormat, _cmbNotesApplyScope;
        private NumericUpDown? _nudNotesSlideNumberStart;
        private ListBox? _lstSelectedNotesSlides;
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public HeaderFooterSettingsForm()
        {
            InitializeComponent();
            InitializeControls();
            LoadDefaultValues();
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="settings">PPT页脚设置</param>
        public HeaderFooterSettingsForm(HeaderFooterSettings settings)
        {
            Settings = settings ?? new HeaderFooterSettings();
            InitializeComponent();
            InitializeControls();

            // 设置所有下拉框文字居中显示
            SetAllComboBoxesTextCenter();

            LoadSettings();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(HeaderFooterSettingsForm));
            SuspendLayout();
            // 
            // HeaderFooterSettingsForm
            // 
            BackColor = Color.FromArgb(240, 240, 240);
            ClientSize = new Size(1184, 861);
            Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon?)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "HeaderFooterSettingsForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "PPT页脚设置";
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreateTabControl();
            CreateDeletionTab();
            CreatePresentationTab();
            CreateSlideTab();
            CreateMasterSlideTab();
            CreateLayoutSlideTab();
            CreateNotesSlideTab();
            CreateActionButtons();
            SetupEventHandlers();
        }

        /// <summary>
        /// 创建选项卡控件
        /// </summary>
        private void CreateTabControl()
        {
            _tabControl = new TabControl
            {
                Location = new Point(20, 20),
                Size = new Size(1150, 770),  // 大幅增加TabControl的宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 9F),
                Appearance = TabAppearance.Normal,
                SizeMode = TabSizeMode.Fixed,
                ItemSize = new Size(180, 35)  // 增加标签页尺寸以更好显示文字
            };

            this.Controls.Add(_tabControl);
        }

        /// <summary>
        /// 创建删除设置选项卡
        /// </summary>
        private void CreateDeletionTab()
        {
            var tabPage = new TabPage("删除设置")
            {
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(0)  // 移除内边距，让Panel填满
            };

            // 创建滚动Panel容器
            _panelDeletion = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(15),  // 增加内边距
                AutoScrollMinSize = new Size(0, 400)  // 增加最小滚动区域高度
            };

            // 删除选项组
            var grpDeletionOptions = new GroupBox
            {
                Text = "删除选项",
                Location = new Point(25, 25),  // 调整位置
                Size = new Size(1080, 140),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkDeleteFooters = new CheckBox
            {
                Text = "删除所有页脚",
                Location = new Point(25, 60),  // 调整X位置，移到第一个位置
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _chkDeleteSlideNumbers = new CheckBox
            {
                Text = "删除所有页码",
                Location = new Point(230, 60),  // 调整X位置，增加间距
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _chkDeleteDateTimes = new CheckBox
            {
                Text = "删除所有日期时间",
                Location = new Point(435, 60),  // 调整X位置，增加间距
                Size = new Size(220, 35),  // 增加宽度和高度以完整显示文字
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            grpDeletionOptions.Controls.AddRange(new Control[] {
                _chkDeleteFooters, _chkDeleteSlideNumbers, _chkDeleteDateTimes
            });

            // 删除范围组
            var grpDeletionScope = new GroupBox
            {
                Text = "删除范围",
                Location = new Point(25, 185),  // 调整Y位置，增加间距
                Size = new Size(1080, 120),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            var lblScope = new Label
            {
                Text = "应用范围：",
                Location = new Point(25, 45),  // 增加Y位置和左边距
                Size = new Size(120, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbDeletionScope = new ComboBox
            {
                Location = new Point(155, 45),  // 调整位置，增加间距
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbDeletionScope.Items.AddRange(new string[] { "所有幻灯片", "指定范围", "选中幻灯片" });
            _cmbDeletionScope.SelectedIndex = 0;

            // 设置下拉框文字居中显示
            SetComboBoxTextCenter(_cmbDeletionScope);

            var lblRange = new Label
            {
                Text = "幻灯片范围：",
                Location = new Point(360, 45),  // 调整位置到同一行，增加间距
                Size = new Size(140, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtSlideRange = new TextBox
            {
                Location = new Point(510, 45),  // 调整位置到同一行，增加间距
                Size = new Size(250, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                Text = "1-",
                TextAlign = HorizontalAlignment.Center
            };

            grpDeletionScope.Controls.AddRange(new Control[] {
                lblScope, _cmbDeletionScope, lblRange, _txtSlideRange
            });

            // 将控件添加到Panel而不是直接添加到TabPage
            _panelDeletion.Controls.AddRange(new Control[] { grpDeletionOptions, grpDeletionScope });

            // 将Panel添加到TabPage
            tabPage.Controls.Add(_panelDeletion);
            _tabControl!.TabPages.Add(tabPage);
        }

        /// <summary>
        /// 创建演示文稿级别设置选项卡
        /// </summary>
        private void CreatePresentationTab()
        {
            var tabPage = new TabPage("演示文稿级别")
            {
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(0)  // 移除内边距，让Panel填满
            };

            // 创建滚动Panel容器
            _panelPresentation = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(15),  // 增加内边距
                AutoScrollMinSize = new Size(0, 600)  // 增加最小滚动区域高度
            };

            // 启用演示文稿级别PPT页脚设置 - 功能开关：控制是否启用演示文稿级别的页眉页脚设置
            _chkPresentationEnabled = new CheckBox
            {
                Text = "启用演示文稿级别PPT页脚设置",
                Location = new Point(25, 25),  // 调整位置
                Size = new Size(420, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 页脚设置组
            var grpFooter = new GroupBox
            {
                Text = "页脚设置",
                Location = new Point(25, 80),  // 调整Y位置，增加间距
                Size = new Size(1080, 110),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkPresentationShowFooter = new CheckBox
            {
                Text = "显示页脚",
                Location = new Point(25, 45),  // 增加Y位置和左边距
                Size = new Size(140, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtPresentationFooter = new TextBox
            {
                Location = new Point(180, 45),  // 调整位置以适应复选框宽度增加
                Size = new Size(870, 35),  // 大幅增加宽度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                PlaceholderText = "输入页脚文本",
                TextAlign = HorizontalAlignment.Center
            };

            grpFooter.Controls.AddRange(new Control[] { _chkPresentationShowFooter, _txtPresentationFooter });

            // 页码设置组
            var grpSlideNumber = new GroupBox
            {
                Text = "页码设置",
                Location = new Point(25, 210),  // 调整位置，向上移动以填补页眉设置组的空间
                Size = new Size(1080, 110),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkPresentationShowSlideNumber = new CheckBox
            {
                Text = "显示页码",
                Location = new Point(25, 45),  // 增加Y位置和左边距
                Size = new Size(140, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            var lblSlideNumberFormat = new Label
            {
                Text = "格式：",
                Location = new Point(180, 45),  // 调整位置以适应复选框宽度增加
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            var txtSlideNumberFormat = new TextBox
            {
                Location = new Point(260, 45),  // 调整位置以适应复选框宽度增加
                Size = new Size(300, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                Text = "幻灯片 #",
                TextAlign = HorizontalAlignment.Center
            };

            grpSlideNumber.Controls.AddRange(new Control[] { _chkPresentationShowSlideNumber, lblSlideNumberFormat, txtSlideNumberFormat });

            // 日期时间设置组
            var grpDateTime = new GroupBox
            {
                Text = "日期时间设置",
                Location = new Point(25, 350),  // 调整位置，增加间距
                Size = new Size(1080, 110),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkPresentationShowDateTime = new CheckBox
            {
                Text = "显示日期时间",
                Location = new Point(25, 45),  // 增加Y位置和左边距
                Size = new Size(160, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _chkPresentationAutoUpdateDateTime = new CheckBox
            {
                Text = "自动更新",
                Location = new Point(200, 45),  // 调整位置以适应前一个复选框宽度增加
                Size = new Size(120, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft,
                Checked = true
            };

            var lblDateTimeFormat = new Label
            {
                Text = "格式：",
                Location = new Point(340, 45),  // 调整位置以适应前面复选框宽度增加
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbPresentationDateTimeFormat = new ComboBox
            {
                Location = new Point(420, 45),  // 调整位置以适应前面复选框宽度增加
                Size = new Size(220, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbPresentationDateTimeFormat.Items.AddRange(new string[] {
                "yyyy/MM/dd", "yyyy-MM-dd", "MM/dd/yyyy", "dd/MM/yyyy", "yyyy年MM月dd日"
            });
            _cmbPresentationDateTimeFormat.SelectedIndex = 0;

            // 设置下拉框文字居中显示
            SetComboBoxTextCenter(_cmbPresentationDateTimeFormat);

            _txtPresentationDateTime = new TextBox
            {
                Location = new Point(660, 45),  // 调整位置，增加间距
                Size = new Size(390, 35),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                PlaceholderText = "自定义文本",
                TextAlign = HorizontalAlignment.Center
            };

            grpDateTime.Controls.AddRange(new Control[] {
                _chkPresentationShowDateTime, _chkPresentationAutoUpdateDateTime,
                lblDateTimeFormat, _cmbPresentationDateTimeFormat, _txtPresentationDateTime
            });

            // 将控件添加到Panel而不是直接添加到TabPage
            _panelPresentation.Controls.AddRange(new Control[] { _chkPresentationEnabled, grpFooter, grpSlideNumber, grpDateTime });

            // 将Panel添加到TabPage
            tabPage.Controls.Add(_panelPresentation);
            _tabControl!.TabPages.Add(tabPage);
        }

        /// <summary>
        /// 创建普通幻灯片设置选项卡
        /// </summary>
        private void CreateSlideTab()
        {
            var tabPage = new TabPage("普通幻灯片")
            {
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(0)  // 移除内边距，让Panel填满
            };

            // 创建滚动Panel容器
            _panelSlide = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(15),  // 增加内边距
                AutoScrollMinSize = new Size(0, 800)  // 大幅增加最小滚动区域高度
            };

            // 启用普通幻灯片PPT页脚设置 - 功能开关：控制是否启用普通幻灯片的页眉页脚设置
            _chkSlideEnabled = new CheckBox
            {
                Text = "启用普通幻灯片PPT页脚设置",
                Location = new Point(25, 25),  // 调整位置
                Size = new Size(420, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 页脚设置组
            _grpSlideFooter = new GroupBox
            {
                Text = "页脚设置",
                Location = new Point(25, 80),  // 调整Y位置，增加间距
                Size = new Size(1080, 110),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            // 启用页脚 - 功能开关：控制是否启用普通幻灯片的页脚功能
            _chkSlideFooterEnabled = new CheckBox
            {
                Text = "启用",
                Location = new Point(25, 45),  // 增加Y位置和左边距
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 显示页脚 - 功能开关：控制页脚是否在幻灯片上可见
            _chkSlideFooterVisible = new CheckBox
            {
                Text = "显示",
                Location = new Point(140, 45),  // 调整位置以适应启用复选框的宽度增加
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtSlideFooter = new TextBox
            {
                Location = new Point(255, 45),  // 调整位置以适应复选框宽度增加
                Size = new Size(795, 35),  // 大幅增加宽度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                PlaceholderText = "输入页脚文本",
                TextAlign = HorizontalAlignment.Center
            };

            _grpSlideFooter.Controls.AddRange(new Control[] { _chkSlideFooterEnabled, _chkSlideFooterVisible, _txtSlideFooter });

            // 页码设置组
            _grpSlideNumber = new GroupBox
            {
                Text = "页码设置",
                Location = new Point(25, 210),  // 调整位置，向上移动以填补页眉设置组的空间
                Size = new Size(1080, 110),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            // 启用页码 - 功能开关：控制是否启用普通幻灯片的页码功能
            _chkSlideNumberEnabled = new CheckBox
            {
                Text = "启用",
                Location = new Point(25, 45),  // 调整位置
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            // 显示页码 - 功能开关：控制页码是否在幻灯片上可见
            _chkSlideNumberVisible = new CheckBox
            {
                Text = "显示",
                Location = new Point(140, 45),  // 调整位置，增加间距
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            var lblFormat = new Label
            {
                Text = "格式：",
                Location = new Point(255, 45),  // 调整位置，增加间距
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbSlideNumberFormat = new ComboBox
            {
                Location = new Point(335, 45),  // 调整位置，增加间距
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbSlideNumberFormat.Items.AddRange(new string[] { "#", "幻灯片 #", "第 # 页", "- # -" });
            _cmbSlideNumberFormat.SelectedIndex = 0;
            SetComboBoxTextCenter(_cmbSlideNumberFormat);

            var lblStart = new Label
            {
                Text = "起始：",
                Location = new Point(530, 45),  // 调整位置，增加间距
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudSlideNumberStart = new NumericUpDown
            {
                Location = new Point(610, 45),  // 调整位置，增加间距
                Size = new Size(120, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                Minimum = 1,
                Maximum = 9999,
                Value = 1,
                TextAlign = HorizontalAlignment.Center
            };

            _grpSlideNumber.Controls.AddRange(new Control[] {
                _chkSlideNumberEnabled, _chkSlideNumberVisible, lblFormat, _cmbSlideNumberFormat, lblStart, _nudSlideNumberStart
            });

            // 日期时间设置组
            _grpSlideDateTime = new GroupBox
            {
                Text = "日期时间设置",
                Location = new Point(25, 350),  // 调整位置，增加间距
                Size = new Size(1080, 140),  // 大幅增加宽度和高度以容纳两行内容
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            // 启用日期时间 - 功能开关：控制是否启用普通幻灯片的日期时间功能
            _chkSlideDateTimeEnabled = new CheckBox
            {
                Text = "启用",
                Location = new Point(25, 45),  // 调整位置
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            // 显示日期时间 - 功能开关：控制日期时间是否在幻灯片上可见
            _chkSlideDateTimeVisible = new CheckBox
            {
                Text = "显示",
                Location = new Point(140, 45),  // 调整位置，增加间距
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            var lblDateFormat = new Label
            {
                Text = "格式：",
                Location = new Point(255, 45),  // 调整位置，增加间距
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbSlideDateTimeFormat = new ComboBox
            {
                Location = new Point(335, 45),  // 调整位置，增加间距
                Size = new Size(200, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbSlideDateTimeFormat.Items.AddRange(new string[] {
                "yyyy/MM/dd", "yyyy-MM-dd", "MM/dd/yyyy", "dd/MM/yyyy", "yyyy年MM月dd日"
            });
            _cmbSlideDateTimeFormat.SelectedIndex = 0;
            SetComboBoxTextCenter(_cmbSlideDateTimeFormat);

            _chkSlideDateTimeAutoUpdate = new CheckBox
            {
                Text = "自动更新",
                Location = new Point(550, 45),  // 调整位置，增加间距
                Size = new Size(120, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkSlideDateTimeUseCustom = new CheckBox
            {
                Text = "使用自定义文本",
                Location = new Point(25, 90),  // 移到第二行，调整位置
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _txtSlideDateTimeCustom = new TextBox
            {
                Location = new Point(220, 90),  // 移到第二行，调整位置
                Size = new Size(830, 35),  // 大幅增加宽度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                PlaceholderText = "输入自定义日期时间文本",
                TextAlign = HorizontalAlignment.Center
            };

            _grpSlideDateTime.Controls.AddRange(new Control[] {
                _chkSlideDateTimeEnabled, _chkSlideDateTimeVisible, lblDateFormat, _cmbSlideDateTimeFormat,
                _chkSlideDateTimeAutoUpdate, _chkSlideDateTimeUseCustom, _txtSlideDateTimeCustom
            });

            // 应用范围组
            var grpApplyScope = new GroupBox
            {
                Text = "应用范围",
                Location = new Point(25, 510),  // 大幅向下调整位置以适应日期时间组的高度增加
                Size = new Size(1080, 150),  // 大幅增加宽度和高度以容纳更多控件
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            var lblApplyScope = new Label
            {
                Text = "应用到：",
                Location = new Point(25, 45),  // 调整位置
                Size = new Size(100, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbSlideApplyScope = new ComboBox
            {
                Location = new Point(135, 45),  // 调整位置，增加间距
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbSlideApplyScope.Items.AddRange(new string[] { "所有幻灯片", "指定范围", "选中幻灯片" });
            _cmbSlideApplyScope.SelectedIndex = 0;
            SetComboBoxTextCenter(_cmbSlideApplyScope);

            var lblSlideRange = new Label
            {
                Text = "幻灯片范围：",
                Location = new Point(335, 45),  // 调整位置，增加间距
                Size = new Size(140, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtSlideRange = new TextBox
            {
                Location = new Point(485, 45),  // 调整位置，增加间距
                Size = new Size(200, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                Text = "1-",
                TextAlign = HorizontalAlignment.Center,
                PlaceholderText = "如：1-5, 1,3,5"
            };

            var lblSelectedSlides = new Label
            {
                Text = "选中的幻灯片：",
                Location = new Point(25, 90),  // 移到第二行，调整位置
                Size = new Size(150, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _lstSelectedSlides = new ListBox
            {
                Location = new Point(185, 90),  // 移到第二行，调整位置
                Size = new Size(865, 50),  // 大幅增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                SelectionMode = SelectionMode.MultiExtended
            };

            // 添加示例幻灯片
            for (int i = 1; i <= 20; i++)
            {
                _lstSelectedSlides.Items.Add($"幻灯片 {i}");
            }

            grpApplyScope.Controls.AddRange(new Control[] {
                lblApplyScope, _cmbSlideApplyScope, lblSlideRange, _txtSlideRange,
                lblSelectedSlides, _lstSelectedSlides
            });

            // 将控件添加到Panel而不是直接添加到TabPage
            _panelSlide.Controls.AddRange(new Control[] {
                _chkSlideEnabled, _grpSlideFooter, _grpSlideNumber, _grpSlideDateTime, grpApplyScope
            });

            // 将Panel添加到TabPage
            tabPage.Controls.Add(_panelSlide);
            _tabControl!.TabPages.Add(tabPage);
        }

        /// <summary>
        /// 创建母版幻灯片设置选项卡
        /// </summary>
        private void CreateMasterSlideTab()
        {
            var tabPage = new TabPage("母版幻灯片")
            {
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(0)  // 移除内边距，让Panel填满
            };

            // 创建滚动Panel容器
            _panelMaster = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(15),  // 增加内边距
                AutoScrollMinSize = new Size(0, 700)  // 大幅增加最小滚动区域高度
            };

            // 启用母版幻灯片PPT页脚设置 - 功能开关：控制是否启用母版幻灯片的页眉页脚设置
            _chkMasterEnabled = new CheckBox
            {
                Text = "启用母版幻灯片PPT页脚设置",
                Location = new Point(25, 25),  // 调整位置
                Size = new Size(420, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold)  // 增加字体大小
            };

            // 页脚设置组
            _grpMasterFooter = new GroupBox
            {
                Text = "页脚设置",
                Location = new Point(25, 80),  // 调整位置，增加间距
                Size = new Size(1080, 110),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkMasterFooterEnabled = new CheckBox
            {
                Text = "启用",
                Location = new Point(25, 45),  // 调整位置
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkMasterFooterVisible = new CheckBox
            {
                Text = "显示",
                Location = new Point(140, 45),  // 调整位置，增加间距
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _txtMasterFooter = new TextBox
            {
                Location = new Point(255, 45),  // 调整位置以适应复选框宽度增加
                Size = new Size(795, 35),  // 大幅增加宽度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                PlaceholderText = "输入页脚文本",
                TextAlign = HorizontalAlignment.Center
            };

            _grpMasterFooter.Controls.AddRange(new Control[] { _chkMasterFooterEnabled, _chkMasterFooterVisible, _txtMasterFooter });

            // 页码设置组
            _grpMasterNumber = new GroupBox
            {
                Text = "页码设置",
                Location = new Point(25, 210),  // 调整位置，向上移动以填补页眉设置组的空间
                Size = new Size(1080, 110),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkMasterNumberEnabled = new CheckBox
            {
                Text = "启用页码",
                Location = new Point(25, 45),  // 调整位置
                Size = new Size(120, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkMasterNumberVisible = new CheckBox
            {
                Text = "显示页码",
                Location = new Point(160, 45),  // 调整位置，增加间距
                Size = new Size(120, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            var lblMasterNumberFormat = new Label
            {
                Text = "格式：",
                Location = new Point(295, 45),  // 调整位置，增加间距
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbMasterNumberFormat = new ComboBox
            {
                Location = new Point(375, 45),  // 调整位置，增加间距
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbMasterNumberFormat.Items.AddRange(new string[] { "#", "幻灯片 #", "第 # 页", "- # -" });
            _cmbMasterNumberFormat.SelectedIndex = 0;

            var lblMasterNumberStart = new Label
            {
                Text = "起始：",
                Location = new Point(570, 45),  // 调整位置，增加间距
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudMasterNumberStart = new NumericUpDown
            {
                Location = new Point(650, 45),  // 调整位置，增加间距
                Size = new Size(120, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                Minimum = 1,
                Maximum = 9999,
                Value = 1,
                TextAlign = HorizontalAlignment.Center
            };

            _grpMasterNumber.Controls.AddRange(new Control[] {
                _chkMasterNumberEnabled, _chkMasterNumberVisible, lblMasterNumberFormat,
                _cmbMasterNumberFormat, lblMasterNumberStart, _nudMasterNumberStart
            });

            // 日期时间设置组
            _grpMasterDateTime = new GroupBox
            {
                Text = "日期时间设置",
                Location = new Point(25, 350),  // 调整位置，增加间距
                Size = new Size(1080, 140),  // 大幅增加宽度和高度以容纳两行内容
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkMasterDateTimeEnabled = new CheckBox
            {
                Text = "启用",
                Location = new Point(25, 45),  // 调整位置
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkMasterDateTimeVisible = new CheckBox
            {
                Text = "显示",
                Location = new Point(140, 45),  // 调整位置，增加间距
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            var lblMasterDateTimeFormat = new Label
            {
                Text = "格式：",
                Location = new Point(255, 45),  // 调整位置，增加间距
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbMasterDateTimeFormat = new ComboBox
            {
                Location = new Point(335, 45),  // 调整位置，增加间距
                Size = new Size(200, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbMasterDateTimeFormat.Items.AddRange(new string[] {
                "yyyy/MM/dd", "yyyy-MM-dd", "MM/dd/yyyy", "dd/MM/yyyy", "yyyy年MM月dd日"
            });
            _cmbMasterDateTimeFormat.SelectedIndex = 0;

            _chkMasterDateTimeAutoUpdate = new CheckBox
            {
                Text = "自动更新",
                Location = new Point(550, 45),  // 调整位置，增加间距
                Size = new Size(120, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkMasterDateTimeUseCustom = new CheckBox
            {
                Text = "使用自定义文本",
                Location = new Point(25, 90),  // 移到第二行，调整位置
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _txtMasterDateTimeCustom = new TextBox
            {
                Location = new Point(220, 90),  // 移到第二行，调整位置
                Size = new Size(830, 35),  // 大幅增加宽度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                PlaceholderText = "输入自定义日期时间文本",
                TextAlign = HorizontalAlignment.Center
            };

            _grpMasterDateTime.Controls.AddRange(new Control[] {
                _chkMasterDateTimeEnabled, _chkMasterDateTimeVisible, lblMasterDateTimeFormat,
                _cmbMasterDateTimeFormat, _chkMasterDateTimeAutoUpdate, _chkMasterDateTimeUseCustom,
                _txtMasterDateTimeCustom
            });

            // 应用选项
            _chkMasterApplyToChildren = new CheckBox
            {
                Text = "应用到所有子幻灯片",
                Location = new Point(25, 510),  // 大幅调整位置以适应前面组的高度增加
                Size = new Size(250, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                Checked = true
            };

            // 将控件添加到Panel而不是直接添加到TabPage
            _panelMaster.Controls.AddRange(new Control[] {
                _chkMasterEnabled, _grpMasterFooter, _grpMasterNumber, _grpMasterDateTime, _chkMasterApplyToChildren
            });

            // 将Panel添加到TabPage
            tabPage.Controls.Add(_panelMaster);
            _tabControl!.TabPages.Add(tabPage);
        }

        /// <summary>
        /// 创建布局幻灯片设置选项卡
        /// </summary>
        private void CreateLayoutSlideTab()
        {
            var tabPage = new TabPage("布局幻灯片")
            {
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(0)  // 移除内边距，让Panel填满
            };

            // 创建滚动Panel容器
            _panelLayout = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(15),  // 增加内边距
                AutoScrollMinSize = new Size(0, 900)  // 大幅增加最小滚动区域高度
            };

            // 启用布局幻灯片PPT页脚设置 - 功能开关：控制是否启用布局幻灯片的页眉页脚设置
            _chkLayoutEnabled = new CheckBox
            {
                Text = "启用布局幻灯片PPT页脚设置",
                Location = new Point(25, 25),  // 调整位置
                Size = new Size(420, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold)  // 增加字体大小
            };

            // 页脚设置组
            _grpLayoutFooter = new GroupBox
            {
                Text = "页脚设置",
                Location = new Point(25, 80),  // 调整位置，增加间距
                Size = new Size(1080, 110),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkLayoutFooterEnabled = new CheckBox
            {
                Text = "启用",
                Location = new Point(25, 45),  // 调整位置
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkLayoutFooterVisible = new CheckBox
            {
                Text = "显示",
                Location = new Point(140, 45),  // 调整位置，增加间距
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _txtLayoutFooter = new TextBox
            {
                Location = new Point(255, 45),  // 调整位置以适应复选框宽度增加
                Size = new Size(795, 35),  // 大幅增加宽度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                PlaceholderText = "输入页脚文本",
                TextAlign = HorizontalAlignment.Center
            };

            _grpLayoutFooter.Controls.AddRange(new Control[] { _chkLayoutFooterEnabled, _chkLayoutFooterVisible, _txtLayoutFooter });

            // 页码设置组
            _grpLayoutNumber = new GroupBox
            {
                Text = "页码设置",
                Location = new Point(25, 210),  // 调整位置，向上移动以填补页眉设置组的空间
                Size = new Size(1080, 110),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkLayoutNumberEnabled = new CheckBox
            {
                Text = "启用",
                Location = new Point(25, 45),  // 调整位置
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkLayoutNumberVisible = new CheckBox
            {
                Text = "显示",
                Location = new Point(140, 45),  // 调整位置，增加间距
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            var lblLayoutNumberFormat = new Label
            {
                Text = "格式：",
                Location = new Point(255, 45),  // 调整位置，增加间距
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbLayoutNumberFormat = new ComboBox
            {
                Location = new Point(335, 45),  // 调整位置，增加间距
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbLayoutNumberFormat.Items.AddRange(new string[] { "#", "幻灯片 #", "第 # 页", "- # -" });
            _cmbLayoutNumberFormat.SelectedIndex = 0;

            var lblLayoutNumberStart = new Label
            {
                Text = "起始：",
                Location = new Point(530, 45),  // 调整位置，增加间距
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudLayoutNumberStart = new NumericUpDown
            {
                Location = new Point(610, 45),  // 调整位置，增加间距
                Size = new Size(120, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                Minimum = 1,
                Maximum = 9999,
                Value = 1,
                TextAlign = HorizontalAlignment.Center
            };

            _grpLayoutNumber.Controls.AddRange(new Control[] {
                _chkLayoutNumberEnabled, _chkLayoutNumberVisible, lblLayoutNumberFormat,
                _cmbLayoutNumberFormat, lblLayoutNumberStart, _nudLayoutNumberStart
            });

            // 日期时间设置组
            _grpLayoutDateTime = new GroupBox
            {
                Text = "日期时间设置",
                Location = new Point(25, 350),  // 调整位置，增加间距
                Size = new Size(1080, 140),  // 大幅增加宽度和高度以容纳两行内容
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkLayoutDateTimeEnabled = new CheckBox
            {
                Text = "启用",
                Location = new Point(25, 45),  // 调整位置
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkLayoutDateTimeVisible = new CheckBox
            {
                Text = "显示",
                Location = new Point(140, 45),  // 调整位置，增加间距
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            var lblLayoutDateTimeFormat = new Label
            {
                Text = "格式：",
                Location = new Point(255, 45),  // 调整位置，增加间距
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbLayoutDateTimeFormat = new ComboBox
            {
                Location = new Point(335, 45),  // 调整位置，增加间距
                Size = new Size(200, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbLayoutDateTimeFormat.Items.AddRange(new string[] {
                "yyyy/MM/dd", "yyyy-MM-dd", "MM/dd/yyyy", "dd/MM/yyyy", "yyyy年MM月dd日"
            });
            _cmbLayoutDateTimeFormat.SelectedIndex = 0;

            _chkLayoutDateTimeAutoUpdate = new CheckBox
            {
                Text = "自动更新",
                Location = new Point(550, 45),  // 调整位置，增加间距
                Size = new Size(120, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkLayoutDateTimeUseCustom = new CheckBox
            {
                Text = "使用自定义文本",
                Location = new Point(25, 90),  // 移到第二行，调整位置
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _txtLayoutDateTimeCustom = new TextBox
            {
                Location = new Point(220, 90),  // 移到第二行，调整位置
                Size = new Size(830, 35),  // 大幅增加宽度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                PlaceholderText = "输入自定义日期时间文本",
                TextAlign = HorizontalAlignment.Center
            };

            _grpLayoutDateTime.Controls.AddRange(new Control[] {
                _chkLayoutDateTimeEnabled, _chkLayoutDateTimeVisible, lblLayoutDateTimeFormat,
                _cmbLayoutDateTimeFormat, _chkLayoutDateTimeAutoUpdate, _chkLayoutDateTimeUseCustom,
                _txtLayoutDateTimeCustom
            });

            // 应用选项
            _chkLayoutApplyToAll = new CheckBox
            {
                Text = "应用到所有布局幻灯片",
                Location = new Point(25, 510),  // 大幅调整位置以适应前面组的高度增加
                Size = new Size(280, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                Checked = true
            };

            // 布局幻灯片列表
            var lblLayoutSlides = new Label
            {
                Text = "选择布局幻灯片：",
                Location = new Point(25, 560),  // 大幅调整Y位置
                Size = new Size(200, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _lstLayoutSlides = new ListBox
            {
                Location = new Point(25, 600),  // 大幅调整Y位置
                Size = new Size(1080, 100),  // 大幅增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                SelectionMode = SelectionMode.MultiExtended
            };

            // 添加示例布局幻灯片
            _lstLayoutSlides.Items.AddRange(new string[] {
                "标题幻灯片", "标题和内容", "节标题", "两栏内容", "比较", "仅标题", "空白", "内容和标题", "图片和标题"
            });

            // 将控件添加到Panel而不是直接添加到TabPage
            _panelLayout.Controls.AddRange(new Control[] {
                _chkLayoutEnabled, _grpLayoutFooter, _grpLayoutNumber, _grpLayoutDateTime,
                _chkLayoutApplyToAll, lblLayoutSlides, _lstLayoutSlides
            });

            // 将Panel添加到TabPage
            tabPage.Controls.Add(_panelLayout);
            _tabControl!.TabPages.Add(tabPage);
        }

        /// <summary>
        /// 创建备注幻灯片设置选项卡
        /// </summary>
        private void CreateNotesSlideTab()
        {
            var tabPage = new TabPage("备注幻灯片")
            {
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(0)  // 移除内边距，让Panel填满
            };

            // 创建滚动Panel容器
            _panelNotes = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(15),  // 增加内边距
                AutoScrollMinSize = new Size(0, 1200)  // 大幅增加最小滚动区域高度以确保应用范围区域完全显示
            };

            // 启用备注幻灯片PPT页脚设置 - 功能开关：控制是否启用备注幻灯片的页眉页脚设置
            _chkNotesEnabled = new CheckBox
            {
                Text = "启用备注幻灯片PPT页脚设置",
                Location = new Point(25, 25),  // 调整位置
                Size = new Size(420, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold)  // 增加字体大小
            };

            // 备注设置选项卡
            _tabNotesSettings = new TabControl
            {
                Location = new Point(25, 80),  // 调整位置
                Size = new Size(1080, 820),  // 进一步增加高度以容纳备注幻灯片的增加内容
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                Appearance = TabAppearance.Normal,
                SizeMode = TabSizeMode.Fixed,
                ItemSize = new Size(180, 35)  // 增加标签页尺寸
            };

            // 备注母版选项卡
            var notesMasterTab = new TabPage("备注母版")
            {
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(15)  // 增加内边距
            };

            _grpNotesMaster = new GroupBox
            {
                Text = "备注母版PPT页脚设置",
                Location = new Point(25, 25),  // 调整位置
                Size = new Size(1020, 600),  // 增加高度以容纳所有内容
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            // 页脚设置
            var grpNotesMasterFooter = new GroupBox
            {
                Text = "页脚设置",
                Location = new Point(25, 40),  // 调整位置
                Size = new Size(970, 100),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkNotesMasterFooterEnabled = new CheckBox
            {
                Text = "启用",
                Location = new Point(25, 40),  // 调整位置
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkNotesMasterFooterVisible = new CheckBox
            {
                Text = "显示",
                Location = new Point(140, 40),  // 调整位置，增加间距
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _txtNotesMasterFooter = new TextBox
            {
                Location = new Point(255, 40),  // 调整位置以适应复选框宽度增加
                Size = new Size(690, 35),  // 大幅增加宽度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                PlaceholderText = "输入页脚文本",
                TextAlign = HorizontalAlignment.Center
            };

            grpNotesMasterFooter.Controls.AddRange(new Control[] { _chkNotesMasterFooterEnabled, _chkNotesMasterFooterVisible, _txtNotesMasterFooter });

            // 页码设置
            var grpNotesMasterNumber = new GroupBox
            {
                Text = "页码设置",
                Location = new Point(25, 160),  // 调整位置，向上移动以填补页眉设置组的空间
                Size = new Size(970, 100),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkNotesMasterNumberEnabled = new CheckBox
            {
                Text = "启用",
                Location = new Point(25, 40),  // 调整位置
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkNotesMasterNumberVisible = new CheckBox
            {
                Text = "显示",
                Location = new Point(140, 40),  // 调整位置，增加间距
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            var lblNotesMasterNumberFormat = new Label
            {
                Text = "格式：",
                Location = new Point(255, 40),  // 调整位置，增加间距
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbNotesMasterNumberFormat = new ComboBox
            {
                Location = new Point(335, 40),  // 调整位置，增加间距
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbNotesMasterNumberFormat.Items.AddRange(new string[] { "#", "幻灯片 #", "第 # 页", "- # -" });
            _cmbNotesMasterNumberFormat.SelectedIndex = 0;

            var lblNotesMasterNumberStart = new Label
            {
                Text = "起始：",
                Location = new Point(530, 40),  // 调整位置，增加间距
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudNotesMasterNumberStart = new NumericUpDown
            {
                Location = new Point(610, 40),  // 调整位置，增加间距
                Size = new Size(120, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                Minimum = 1,
                Maximum = 9999,
                Value = 1,
                TextAlign = HorizontalAlignment.Center
            };

            grpNotesMasterNumber.Controls.AddRange(new Control[] {
                _chkNotesMasterNumberEnabled, _chkNotesMasterNumberVisible, lblNotesMasterNumberFormat,
                _cmbNotesMasterNumberFormat, lblNotesMasterNumberStart, _nudNotesMasterNumberStart
            });

            // 日期时间设置
            var grpNotesMasterDateTime = new GroupBox
            {
                Text = "日期时间设置",
                Location = new Point(25, 280),  // 调整位置以适应前面组的高度增加
                Size = new Size(970, 120),  // 大幅增加宽度和高度以容纳两行内容
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkNotesMasterDateTimeEnabled = new CheckBox
            {
                Text = "启用",
                Location = new Point(25, 40),  // 调整位置
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkNotesMasterDateTimeVisible = new CheckBox
            {
                Text = "显示",
                Location = new Point(140, 40),  // 调整位置，增加间距
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            var lblNotesMasterDateTimeFormat = new Label
            {
                Text = "格式：",
                Location = new Point(255, 40),  // 调整位置，增加间距
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbNotesMasterDateTimeFormat = new ComboBox
            {
                Location = new Point(335, 40),  // 调整位置，增加间距
                Size = new Size(200, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbNotesMasterDateTimeFormat.Items.AddRange(new string[] {
                "yyyy/MM/dd", "yyyy-MM-dd", "MM/dd/yyyy", "dd/MM/yyyy", "yyyy年MM月dd日"
            });
            _cmbNotesMasterDateTimeFormat.SelectedIndex = 0;

            _chkNotesMasterDateTimeAutoUpdate = new CheckBox
            {
                Text = "自动更新",
                Location = new Point(550, 40),  // 调整位置，增加间距
                Size = new Size(120, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkNotesMasterDateTimeUseCustom = new CheckBox
            {
                Text = "使用自定义文本",
                Location = new Point(25, 80),  // 移到第二行，调整位置
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _txtNotesMasterDateTimeCustom = new TextBox
            {
                Location = new Point(220, 80),  // 移到第二行，调整位置
                Size = new Size(725, 35),  // 大幅增加宽度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                PlaceholderText = "输入自定义日期时间文本",
                TextAlign = HorizontalAlignment.Center
            };

            grpNotesMasterDateTime.Controls.AddRange(new Control[] {
                _chkNotesMasterDateTimeEnabled, _chkNotesMasterDateTimeVisible, lblNotesMasterDateTimeFormat,
                _cmbNotesMasterDateTimeFormat, _chkNotesMasterDateTimeAutoUpdate, _chkNotesMasterDateTimeUseCustom,
                _txtNotesMasterDateTimeCustom
            });

            _chkNotesMasterApplyToChildren = new CheckBox
            {
                Text = "应用到所有子备注幻灯片",
                Location = new Point(25, 420),  // 调整位置以适应前面组的高度增加
                Size = new Size(300, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                Checked = true
            };

            _grpNotesMaster.Controls.AddRange(new Control[] {
                grpNotesMasterFooter, grpNotesMasterNumber, grpNotesMasterDateTime, _chkNotesMasterApplyToChildren
            });

            notesMasterTab.Controls.Add(_grpNotesMaster);

            // 备注幻灯片选项卡
            var notesSlideTab = new TabPage("备注幻灯片")
            {
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(15)  // 增加内边距
            };

            _grpNotesSlide = new GroupBox
            {
                Text = "备注幻灯片PPT页脚设置",
                Location = new Point(25, 25),  // 调整位置
                Size = new Size(1020, 650),  // 进一步增加高度以容纳增加的应用范围设置组
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            // 页脚设置
            var grpNotesSlideFooter = new GroupBox
            {
                Text = "页脚设置",
                Location = new Point(25, 40),  // 调整位置
                Size = new Size(970, 100),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkNotesSlideFooterEnabled = new CheckBox
            {
                Text = "启用",
                Location = new Point(25, 40),  // 调整位置
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkNotesSlideFooterVisible = new CheckBox
            {
                Text = "显示",
                Location = new Point(140, 40),  // 调整位置，增加间距
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _txtNotesSlideFooter = new TextBox
            {
                Location = new Point(255, 40),  // 调整位置以适应复选框宽度增加
                Size = new Size(690, 35),  // 大幅增加宽度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                PlaceholderText = "输入页脚文本",
                TextAlign = HorizontalAlignment.Center
            };

            grpNotesSlideFooter.Controls.AddRange(new Control[] { _chkNotesSlideFooterEnabled, _chkNotesSlideFooterVisible, _txtNotesSlideFooter });

            // 页码设置
            var grpNotesSlideNumber = new GroupBox
            {
                Text = "页码设置",
                Location = new Point(25, 160),  // 调整位置，向上移动以填补页眉设置组的空间
                Size = new Size(970, 100),  // 大幅增加宽度和高度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkNotesSlideNumberEnabled = new CheckBox
            {
                Text = "启用",
                Location = new Point(25, 40),  // 调整位置
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkNotesSlideNumberVisible = new CheckBox
            {
                Text = "显示",
                Location = new Point(140, 40),  // 调整位置，增加间距
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            var lblNotesSlideNumberFormat = new Label
            {
                Text = "格式：",
                Location = new Point(255, 40),  // 调整位置，增加间距
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbNotesSlideNumberFormat = new ComboBox
            {
                Location = new Point(335, 40),  // 调整位置，增加间距
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbNotesSlideNumberFormat.Items.AddRange(new string[] { "#", "幻灯片 #", "第 # 页", "- # -" });
            _cmbNotesSlideNumberFormat.SelectedIndex = 0;

            var lblNotesSlideNumberStart = new Label
            {
                Text = "起始：",
                Location = new Point(530, 40),  // 调整位置，增加间距
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudNotesSlideNumberStart = new NumericUpDown
            {
                Location = new Point(610, 40),  // 调整位置，增加间距
                Size = new Size(120, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                Minimum = 1,
                Maximum = 9999,
                Value = 1,
                TextAlign = HorizontalAlignment.Center
            };

            grpNotesSlideNumber.Controls.AddRange(new Control[] {
                _chkNotesSlideNumberEnabled, _chkNotesSlideNumberVisible, lblNotesSlideNumberFormat,
                _cmbNotesSlideNumberFormat, lblNotesSlideNumberStart, _nudNotesSlideNumberStart
            });

            // 日期时间设置
            var grpNotesSlideDateTime = new GroupBox
            {
                Text = "日期时间设置",
                Location = new Point(25, 280),  // 调整位置，增加间距
                Size = new Size(970, 120),  // 大幅增加宽度和高度以容纳两行内容
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            _chkNotesSlideeDateTimeEnabled = new CheckBox
            {
                Text = "启用",
                Location = new Point(25, 40),  // 调整位置
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkNotesSlideeDateTimeVisible = new CheckBox
            {
                Text = "显示",
                Location = new Point(140, 40),  // 调整位置，增加间距
                Size = new Size(100, 35),  // 增加宽度和高度以更好显示文字
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            var lblNotesSlideeDateTimeFormat = new Label
            {
                Text = "格式：",
                Location = new Point(255, 40),  // 调整位置，增加间距
                Size = new Size(70, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbNotesSlideeDateTimeFormat = new ComboBox
            {
                Location = new Point(335, 40),  // 调整位置，增加间距
                Size = new Size(200, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbNotesSlideeDateTimeFormat.Items.AddRange(new string[] {
                "yyyy/MM/dd", "yyyy-MM-dd", "MM/dd/yyyy", "dd/MM/yyyy", "yyyy年MM月dd日"
            });
            _cmbNotesSlideeDateTimeFormat.SelectedIndex = 0;

            _chkNotesSlideeDateTimeAutoUpdate = new CheckBox
            {
                Text = "自动更新",
                Location = new Point(550, 40),  // 调整位置，增加间距
                Size = new Size(120, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _chkNotesSlideeDateTimeUseCustom = new CheckBox
            {
                Text = "使用自定义文本",
                Location = new Point(25, 80),  // 移到第二行，调整位置
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F)  // 增加字体大小
            };

            _txtNotesSlideeDateTimeCustom = new TextBox
            {
                Location = new Point(220, 80),  // 移到第二行，调整位置
                Size = new Size(725, 35),  // 大幅增加宽度以适应新窗口尺寸
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                PlaceholderText = "输入自定义日期时间文本",
                TextAlign = HorizontalAlignment.Center
            };

            grpNotesSlideDateTime.Controls.AddRange(new Control[] {
                _chkNotesSlideeDateTimeEnabled, _chkNotesSlideeDateTimeVisible, lblNotesSlideeDateTimeFormat,
                _cmbNotesSlideeDateTimeFormat, _chkNotesSlideeDateTimeAutoUpdate, _chkNotesSlideeDateTimeUseCustom,
                _txtNotesSlideeDateTimeCustom
            });

            // 应用范围设置
            var grpNotesApplyScope = new GroupBox
            {
                Text = "应用范围",
                Location = new Point(25, 420),  // 大幅调整位置以适应前面组的高度增加
                Size = new Size(970, 190),  // 进一步增加高度以容纳增加的ListBox高度
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)  // 增加字体大小
            };

            var lblNotesApplyScope = new Label
            {
                Text = "应用到：",
                Location = new Point(25, 40),  // 调整位置
                Size = new Size(120, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbNotesApplyScope = new ComboBox
            {
                Location = new Point(145, 40),  // 调整位置，增加间距
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbNotesApplyScope.Items.AddRange(new string[] { "所有备注幻灯片", "指定范围", "选中备注幻灯片" });
            _cmbNotesApplyScope.SelectedIndex = 0;

            var lblNotesSlideRange = new Label
            {
                Text = "备注幻灯片范围：",
                Location = new Point(340, 40),  // 调整位置，增加间距
                Size = new Size(180, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtNotesSlideRange = new TextBox
            {
                Location = new Point(530, 40),  // 调整位置，增加间距
                Size = new Size(200, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                Text = "1-",
                TextAlign = HorizontalAlignment.Center,
                PlaceholderText = "如：1-5, 1,3,5"
            };

            var lblSelectedNotesSlides = new Label
            {
                Text = "选中的备注幻灯片：",
                Location = new Point(25, 100),  // 移到第二行，调整位置
                Size = new Size(200, 35),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                TextAlign = ContentAlignment.MiddleLeft
            };

            _lstSelectedNotesSlides = new ListBox
            {
                Location = new Point(235, 100),  // 移到第二行，调整位置
                Size = new Size(710, 60),  // 增加高度以显示更多内容
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                SelectionMode = SelectionMode.MultiExtended
            };

            // 添加示例备注幻灯片
            for (int i = 1; i <= 10; i++)
            {
                _lstSelectedNotesSlides.Items.Add($"备注 {i}");
            }

            grpNotesApplyScope.Controls.AddRange(new Control[] {
                lblNotesApplyScope, _cmbNotesApplyScope, lblNotesSlideRange, _txtNotesSlideRange,
                lblSelectedNotesSlides, _lstSelectedNotesSlides
            });

            _grpNotesSlide.Controls.AddRange(new Control[] {
                grpNotesSlideFooter, grpNotesSlideNumber, grpNotesSlideDateTime, grpNotesApplyScope
            });

            notesSlideTab.Controls.Add(_grpNotesSlide);

            _tabNotesSettings.TabPages.AddRange(new TabPage[] { notesMasterTab, notesSlideTab });

            // 将控件添加到Panel而不是直接添加到TabPage
            _panelNotes.Controls.AddRange(new Control[] { _chkNotesEnabled, _tabNotesSettings });

            // 将Panel添加到TabPage
            tabPage.Controls.Add(_panelNotes);
            _tabControl!.TabPages.Add(tabPage);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            // TabControl占据位置：Y=20到Y=790 (20 + 770)
            // 计算按钮位置：TabControl底部 + 间距 + 按钮位置
            var buttonY = 20 + 770 + 10;  // TabControl底部 + 15px间距 = 805
            var buttonWidth = 100;  // 按钮宽度
            var buttonHeight = 35;  // 按钮高度
            var buttonSpacing = 25;  // 按钮间距

            // 计算按钮X位置：从窗口右边对齐
            var totalButtonsWidth = buttonWidth * 3 + buttonSpacing * 2;  // 3个按钮 + 2个间距 = 350
            var buttonStartX = 1200 - 40 - totalButtonsWidth;  // 窗口宽度 - 右边距40 - 按钮总宽度 = 810

            _btnOK = new Button
            {
                Text = "确定",
                Location = new Point(buttonStartX, buttonY),
                Size = new Size(buttonWidth, buttonHeight),
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _btnOK.FlatAppearance.BorderSize = 0;

            _btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(buttonStartX + buttonWidth + buttonSpacing, buttonY),
                Size = new Size(buttonWidth, buttonHeight),
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _btnCancel.FlatAppearance.BorderSize = 0;

            _btnReset = new Button
            {
                Text = "重置",
                Location = new Point(buttonStartX + (buttonWidth + buttonSpacing) * 2, buttonY),
                Size = new Size(buttonWidth, buttonHeight),
                Font = new Font("Microsoft YaHei UI", 10F),  // 增加字体大小
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _btnReset.FlatAppearance.BorderSize = 0;

            // 添加按钮到窗体，确保它们在TabControl之上
            this.Controls.AddRange(new Control[] { _btnOK, _btnCancel, _btnReset });

            // 确保按钮在最前面显示
            _btnOK.BringToFront();
            _btnCancel.BringToFront();
            _btnReset.BringToFront();
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 设置下拉框文字居中显示
        /// </summary>
        /// <param name="comboBox">下拉框控件</param>
        private static void SetComboBoxTextCenter(ComboBox? comboBox)
        {
            if (comboBox == null) return;

            comboBox.DrawMode = DrawMode.OwnerDrawFixed;
            comboBox.DrawItem += (s, e) => {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = comboBox.Items[e.Index].ToString();
                    var sf = new StringFormat { Alignment = StringAlignment.Center, LineAlignment = StringAlignment.Center };
                    e.Graphics.DrawString(text, e.Font ?? comboBox.Font, new SolidBrush(e.ForeColor), e.Bounds, sf);
                }
            };
        }

        /// <summary>
        /// 设置所有下拉框文字居中显示
        /// </summary>
        private void SetAllComboBoxesTextCenter()
        {
            // 为所有下拉框设置文字居中显示
            SetComboBoxTextCenter(_cmbMasterNumberFormat);
            SetComboBoxTextCenter(_cmbMasterDateTimeFormat);
            SetComboBoxTextCenter(_cmbLayoutNumberFormat);
            SetComboBoxTextCenter(_cmbLayoutDateTimeFormat);
            SetComboBoxTextCenter(_cmbNotesMasterNumberFormat);
            SetComboBoxTextCenter(_cmbNotesMasterDateTimeFormat);
            SetComboBoxTextCenter(_cmbNotesSlideNumberFormat);
            SetComboBoxTextCenter(_cmbNotesSlideeDateTimeFormat);
            SetComboBoxTextCenter(_cmbNotesApplyScope);
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 设置事件处理程序
        /// </summary>
        private void SetupEventHandlers()
        {
            // 按钮事件
            if (_btnOK != null)
                _btnOK.Click += BtnOK_Click;
            if (_btnCancel != null)
                _btnCancel.Click += BtnCancel_Click;
            if (_btnReset != null)
                _btnReset.Click += BtnReset_Click;

            // 启用复选框事件
            if (_chkPresentationEnabled != null)
                _chkPresentationEnabled.CheckedChanged += ChkPresentationEnabled_CheckedChanged;
            if (_chkSlideEnabled != null)
                _chkSlideEnabled.CheckedChanged += ChkSlideEnabled_CheckedChanged;
            if (_chkMasterEnabled != null)
                _chkMasterEnabled.CheckedChanged += ChkMasterEnabled_CheckedChanged;
            if (_chkLayoutEnabled != null)
                _chkLayoutEnabled.CheckedChanged += ChkLayoutEnabled_CheckedChanged;
            if (_chkNotesEnabled != null)
                _chkNotesEnabled.CheckedChanged += ChkNotesEnabled_CheckedChanged;

            // 删除范围变更事件
            if (_cmbDeletionScope != null)
                _cmbDeletionScope.SelectedIndexChanged += CmbDeletionScope_SelectedIndexChanged;

            // 应用范围变更事件
            if (_cmbSlideApplyScope != null)
                _cmbSlideApplyScope.SelectedIndexChanged += CmbSlideApplyScope_SelectedIndexChanged;

            // 布局应用选项事件
            if (_chkLayoutApplyToAll != null)
                _chkLayoutApplyToAll.CheckedChanged += ChkLayoutApplyToAll_CheckedChanged;

            // 日期时间自定义文本事件
            if (_chkSlideDateTimeUseCustom != null)
                _chkSlideDateTimeUseCustom.CheckedChanged += ChkSlideDateTimeUseCustom_CheckedChanged;
            if (_chkMasterDateTimeUseCustom != null)
                _chkMasterDateTimeUseCustom.CheckedChanged += ChkMasterDateTimeUseCustom_CheckedChanged;
            if (_chkLayoutDateTimeUseCustom != null)
                _chkLayoutDateTimeUseCustom.CheckedChanged += ChkLayoutDateTimeUseCustom_CheckedChanged;
            if (_chkNotesMasterDateTimeUseCustom != null)
                _chkNotesMasterDateTimeUseCustom.CheckedChanged += ChkNotesMasterDateTimeUseCustom_CheckedChanged;
            if (_chkNotesSlideeDateTimeUseCustom != null)
                _chkNotesSlideeDateTimeUseCustom.CheckedChanged += ChkNotesSlideeDateTimeUseCustom_CheckedChanged;

            // 备注应用范围变更事件
            if (_cmbNotesApplyScope != null)
                _cmbNotesApplyScope.SelectedIndexChanged += CmbNotesApplyScope_SelectedIndexChanged;
        }

        /// <summary>
        /// 确定按钮点击事件 - 保存配置并关闭窗口
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 保存设置到配置对象
                SaveSettings();

                // 保存配置到配置文件
                var config = ConfigService.Instance.GetConfig();
                config.HeaderFooterSettings = Settings;
                ConfigService.Instance.UpdateConfig(config);

                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件 - 不保存配置直接关闭窗口
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            // 取消操作，不保存任何配置更改
            DialogResult = DialogResult.Cancel;
            Close();
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            // 直接重置，不需要确认对话框
            Settings = new HeaderFooterSettings();
            LoadSettings();
        }

        /// <summary>
        /// 演示文稿启用状态变更事件
        /// </summary>
        private void ChkPresentationEnabled_CheckedChanged(object? sender, EventArgs e)
        {
            UpdatePresentationControlsState();
        }

        /// <summary>
        /// 幻灯片启用状态变更事件
        /// </summary>
        private void ChkSlideEnabled_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateSlideControlsState();
        }

        /// <summary>
        /// 母版启用状态变更事件
        /// </summary>
        private void ChkMasterEnabled_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateMasterControlsState();
        }

        /// <summary>
        /// 布局启用状态变更事件
        /// </summary>
        private void ChkLayoutEnabled_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateLayoutControlsState();
        }

        /// <summary>
        /// 备注启用状态变更事件
        /// </summary>
        private void ChkNotesEnabled_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateNotesControlsState();
        }

        /// <summary>
        /// 删除范围变更事件
        /// </summary>
        private void CmbDeletionScope_SelectedIndexChanged(object? sender, EventArgs e)
        {
            UpdateDeletionScopeControls();
        }

        /// <summary>
        /// 幻灯片应用范围变更事件
        /// </summary>
        private void CmbSlideApplyScope_SelectedIndexChanged(object? sender, EventArgs e)
        {
            UpdateSlideApplyScopeControls();
        }

        /// <summary>
        /// 布局应用到所有选项变更事件
        /// </summary>
        private void ChkLayoutApplyToAll_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateLayoutApplyControls();
        }

        #endregion

        #region 数据加载和保存

        /// <summary>
        /// 加载默认值
        /// </summary>
        private void LoadDefaultValues()
        {
            Settings = new HeaderFooterSettings();
            LoadSettings();
        }

        /// <summary>
        /// 加载设置
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                // 加载删除设置
                LoadDeletionSettings();

                // 加载演示文稿设置
                LoadPresentationSettings();

                // 加载幻灯片设置
                LoadSlideSettings();

                // 加载母版设置
                LoadMasterSettings();

                // 加载布局设置
                LoadLayoutSettings();

                // 加载备注设置
                LoadNotesSettings();

                // 更新控件状态
                UpdateAllControlsState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载设置时发生错误：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 加载删除设置
        /// </summary>
        private void LoadDeletionSettings()
        {
            var deletion = Settings.DeletionSettings;

            if (_chkDeleteFooters != null)
                _chkDeleteFooters.Checked = deletion.DeleteAllFooters;
            if (_chkDeleteSlideNumbers != null)
                _chkDeleteSlideNumbers.Checked = deletion.DeleteAllSlideNumbers;
            if (_chkDeleteDateTimes != null)
                _chkDeleteDateTimes.Checked = deletion.DeleteAllDateTimes;

            if (_cmbDeletionScope != null)
            {
                _cmbDeletionScope.SelectedIndex = deletion.DeletionScope switch
                {
                    "All" => 0,
                    "SlideRange" => 1,
                    "SelectedSlides" => 2,
                    _ => 0
                };
            }

            if (_txtSlideRange != null)
                _txtSlideRange.Text = deletion.SlideRange;
        }

        /// <summary>
        /// 加载演示文稿设置
        /// </summary>
        private void LoadPresentationSettings()
        {
            var presentation = Settings.PresentationSettings;

            if (_chkPresentationEnabled != null)
                _chkPresentationEnabled.Checked = presentation.IsEnabled;
            if (_chkPresentationShowFooter != null)
                _chkPresentationShowFooter.Checked = presentation.ShowFooter;
            if (_txtPresentationFooter != null)
                _txtPresentationFooter.Text = presentation.FooterText;

            if (_chkPresentationShowSlideNumber != null)
                _chkPresentationShowSlideNumber.Checked = presentation.ShowSlideNumber;
            if (_chkPresentationShowDateTime != null)
                _chkPresentationShowDateTime.Checked = presentation.ShowDateTime;
            if (_txtPresentationDateTime != null)
                _txtPresentationDateTime.Text = presentation.DateTimeText;
            if (_cmbPresentationDateTimeFormat != null)
                _cmbPresentationDateTimeFormat.Text = presentation.DateTimeFormat;
            if (_chkPresentationAutoUpdateDateTime != null)
                _chkPresentationAutoUpdateDateTime.Checked = presentation.AutoUpdateDateTime;
        }

        /// <summary>
        /// 加载幻灯片设置
        /// </summary>
        private void LoadSlideSettings()
        {
            var slide = Settings.SlideSettings;

            if (_chkSlideEnabled != null)
                _chkSlideEnabled.Checked = slide.IsEnabled;

            // 页脚设置
            if (_chkSlideFooterEnabled != null)
                _chkSlideFooterEnabled.Checked = slide.Footer.IsEnabled;
            if (_chkSlideFooterVisible != null)
                _chkSlideFooterVisible.Checked = slide.Footer.IsVisible;
            if (_txtSlideFooter != null)
                _txtSlideFooter.Text = slide.Footer.Text;

            // 页码设置
            if (_chkSlideNumberEnabled != null)
                _chkSlideNumberEnabled.Checked = slide.SlideNumber.IsEnabled;
            if (_chkSlideNumberVisible != null)
                _chkSlideNumberVisible.Checked = slide.SlideNumber.IsVisible;
            if (_cmbSlideNumberFormat != null)
                _cmbSlideNumberFormat.Text = slide.SlideNumber.Format;
            if (_nudSlideNumberStart != null)
                _nudSlideNumberStart.Value = slide.SlideNumber.StartNumber;

            // 日期时间设置
            if (_chkSlideDateTimeEnabled != null)
                _chkSlideDateTimeEnabled.Checked = slide.DateTime.IsEnabled;
            if (_chkSlideDateTimeVisible != null)
                _chkSlideDateTimeVisible.Checked = slide.DateTime.IsVisible;
            if (_cmbSlideDateTimeFormat != null)
                _cmbSlideDateTimeFormat.Text = slide.DateTime.Format;
            if (_chkSlideDateTimeAutoUpdate != null)
                _chkSlideDateTimeAutoUpdate.Checked = slide.DateTime.AutoUpdate;
            if (_chkSlideDateTimeUseCustom != null)
                _chkSlideDateTimeUseCustom.Checked = slide.DateTime.UseCustomText;
            if (_txtSlideDateTimeCustom != null)
                _txtSlideDateTimeCustom.Text = slide.DateTime.CustomText;

            // 应用范围
            if (_cmbSlideApplyScope != null)
            {
                _cmbSlideApplyScope.SelectedIndex = slide.ApplyScope switch
                {
                    "AllSlides" => 0,
                    "SlideRange" => 1,
                    "SelectedSlides" => 2,
                    _ => 0
                };
            }

            if (_txtSlideRange != null)
                _txtSlideRange.Text = slide.SlideRange;

            // 选中的幻灯片
            if (_lstSelectedSlides != null)
            {
                _lstSelectedSlides.ClearSelected();
                foreach (var index in slide.SelectedSlideIndexes)
                {
                    if (index >= 0 && index < _lstSelectedSlides.Items.Count)
                        _lstSelectedSlides.SetSelected(index, true);
                }
            }
        }

        /// <summary>
        /// 加载母版设置
        /// </summary>
        private void LoadMasterSettings()
        {
            var master = Settings.MasterSlideSettings;

            if (_chkMasterEnabled != null)
                _chkMasterEnabled.Checked = master.IsEnabled;

            // 页脚设置
            if (_chkMasterFooterEnabled != null)
                _chkMasterFooterEnabled.Checked = master.Footer.IsEnabled;
            if (_chkMasterFooterVisible != null)
                _chkMasterFooterVisible.Checked = master.Footer.IsVisible;
            if (_txtMasterFooter != null)
                _txtMasterFooter.Text = master.Footer.Text;



            // 页码设置
            if (_chkMasterNumberEnabled != null)
                _chkMasterNumberEnabled.Checked = master.SlideNumber.IsEnabled;
            if (_chkMasterNumberVisible != null)
                _chkMasterNumberVisible.Checked = master.SlideNumber.IsVisible;
            if (_cmbMasterNumberFormat != null)
                _cmbMasterNumberFormat.Text = master.SlideNumber.Format;
            if (_nudMasterNumberStart != null)
                _nudMasterNumberStart.Value = master.SlideNumber.StartNumber;

            // 日期时间设置
            if (_chkMasterDateTimeEnabled != null)
                _chkMasterDateTimeEnabled.Checked = master.DateTime.IsEnabled;
            if (_chkMasterDateTimeVisible != null)
                _chkMasterDateTimeVisible.Checked = master.DateTime.IsVisible;
            if (_cmbMasterDateTimeFormat != null)
                _cmbMasterDateTimeFormat.Text = master.DateTime.Format;
            if (_chkMasterDateTimeAutoUpdate != null)
                _chkMasterDateTimeAutoUpdate.Checked = master.DateTime.AutoUpdate;
            if (_chkMasterDateTimeUseCustom != null)
                _chkMasterDateTimeUseCustom.Checked = master.DateTime.UseCustomText;
            if (_txtMasterDateTimeCustom != null)
                _txtMasterDateTimeCustom.Text = master.DateTime.CustomText;

            // 应用到子幻灯片
            if (_chkMasterApplyToChildren != null)
                _chkMasterApplyToChildren.Checked = master.ApplyToChildSlides;
        }

        /// <summary>
        /// 加载布局设置
        /// </summary>
        private void LoadLayoutSettings()
        {
            var layout = Settings.LayoutSlideSettings;

            if (_chkLayoutEnabled != null)
                _chkLayoutEnabled.Checked = layout.IsEnabled;

            if (_chkLayoutApplyToAll != null)
                _chkLayoutApplyToAll.Checked = layout.ApplyToAllLayouts;

            // 页脚设置
            if (_chkLayoutFooterEnabled != null)
                _chkLayoutFooterEnabled.Checked = layout.Footer.IsEnabled;
            if (_chkLayoutFooterVisible != null)
                _chkLayoutFooterVisible.Checked = layout.Footer.IsVisible;
            if (_txtLayoutFooter != null)
                _txtLayoutFooter.Text = layout.Footer.Text;



            // 页码设置
            if (_chkLayoutNumberEnabled != null)
                _chkLayoutNumberEnabled.Checked = layout.SlideNumber.IsEnabled;
            if (_chkLayoutNumberVisible != null)
                _chkLayoutNumberVisible.Checked = layout.SlideNumber.IsVisible;
            if (_cmbLayoutNumberFormat != null)
                _cmbLayoutNumberFormat.Text = layout.SlideNumber.Format;
            if (_nudLayoutNumberStart != null)
                _nudLayoutNumberStart.Value = layout.SlideNumber.StartNumber;

            // 日期时间设置
            if (_chkLayoutDateTimeEnabled != null)
                _chkLayoutDateTimeEnabled.Checked = layout.DateTime.IsEnabled;
            if (_chkLayoutDateTimeVisible != null)
                _chkLayoutDateTimeVisible.Checked = layout.DateTime.IsVisible;
            if (_cmbLayoutDateTimeFormat != null)
                _cmbLayoutDateTimeFormat.Text = layout.DateTime.Format;
            if (_chkLayoutDateTimeAutoUpdate != null)
                _chkLayoutDateTimeAutoUpdate.Checked = layout.DateTime.AutoUpdate;
            if (_chkLayoutDateTimeUseCustom != null)
                _chkLayoutDateTimeUseCustom.Checked = layout.DateTime.UseCustomText;
            if (_txtLayoutDateTimeCustom != null)
                _txtLayoutDateTimeCustom.Text = layout.DateTime.CustomText;

            // 选中的布局幻灯片
            if (_lstLayoutSlides != null)
            {
                _lstLayoutSlides.ClearSelected();
                foreach (var index in layout.SelectedLayoutIndexes)
                {
                    if (index >= 0 && index < _lstLayoutSlides.Items.Count)
                        _lstLayoutSlides.SetSelected(index, true);
                }
            }
        }

        /// <summary>
        /// 加载备注设置
        /// </summary>
        private void LoadNotesSettings()
        {
            var notes = Settings.NotesSlideSettings;

            if (_chkNotesEnabled != null)
                _chkNotesEnabled.Checked = notes.IsEnabled;

            // 备注母版设置
            var notesMaster = notes.NotesMaster;
            if (_chkNotesMasterFooterEnabled != null)
                _chkNotesMasterFooterEnabled.Checked = notesMaster.Footer.IsEnabled;
            if (_chkNotesMasterFooterVisible != null)
                _chkNotesMasterFooterVisible.Checked = notesMaster.Footer.IsVisible;
            if (_txtNotesMasterFooter != null)
                _txtNotesMasterFooter.Text = notesMaster.Footer.Text;



            if (_chkNotesMasterNumberEnabled != null)
                _chkNotesMasterNumberEnabled.Checked = notesMaster.SlideNumber.IsEnabled;
            if (_chkNotesMasterNumberVisible != null)
                _chkNotesMasterNumberVisible.Checked = notesMaster.SlideNumber.IsVisible;
            if (_cmbNotesMasterNumberFormat != null)
                _cmbNotesMasterNumberFormat.Text = notesMaster.SlideNumber.Format;
            if (_nudNotesMasterNumberStart != null)
                _nudNotesMasterNumberStart.Value = notesMaster.SlideNumber.StartNumber;

            if (_chkNotesMasterDateTimeEnabled != null)
                _chkNotesMasterDateTimeEnabled.Checked = notesMaster.DateTime.IsEnabled;
            if (_chkNotesMasterDateTimeVisible != null)
                _chkNotesMasterDateTimeVisible.Checked = notesMaster.DateTime.IsVisible;
            if (_cmbNotesMasterDateTimeFormat != null)
                _cmbNotesMasterDateTimeFormat.Text = notesMaster.DateTime.Format;
            if (_chkNotesMasterDateTimeAutoUpdate != null)
                _chkNotesMasterDateTimeAutoUpdate.Checked = notesMaster.DateTime.AutoUpdate;

            if (_chkNotesMasterApplyToChildren != null)
                _chkNotesMasterApplyToChildren.Checked = notesMaster.ApplyToChildSlides;

            // 备注幻灯片设置
            var notesSlide = notes.NotesSlide;
            if (_chkNotesSlideFooterEnabled != null)
                _chkNotesSlideFooterEnabled.Checked = notesSlide.Footer.IsEnabled;
            if (_chkNotesSlideFooterVisible != null)
                _chkNotesSlideFooterVisible.Checked = notesSlide.Footer.IsVisible;
            if (_txtNotesSlideFooter != null)
                _txtNotesSlideFooter.Text = notesSlide.Footer.Text;



            if (_chkNotesSlideNumberEnabled != null)
                _chkNotesSlideNumberEnabled.Checked = notesSlide.SlideNumber.IsEnabled;
            if (_chkNotesSlideNumberVisible != null)
                _chkNotesSlideNumberVisible.Checked = notesSlide.SlideNumber.IsVisible;
            if (_cmbNotesSlideNumberFormat != null)
                _cmbNotesSlideNumberFormat.Text = notesSlide.SlideNumber.Format;
            if (_nudNotesSlideNumberStart != null)
                _nudNotesSlideNumberStart.Value = notesSlide.SlideNumber.StartNumber;

            if (_chkNotesSlideeDateTimeEnabled != null)
                _chkNotesSlideeDateTimeEnabled.Checked = notesSlide.DateTime.IsEnabled;
            if (_chkNotesSlideeDateTimeVisible != null)
                _chkNotesSlideeDateTimeVisible.Checked = notesSlide.DateTime.IsVisible;
            if (_cmbNotesSlideeDateTimeFormat != null)
                _cmbNotesSlideeDateTimeFormat.Text = notesSlide.DateTime.Format;
            if (_chkNotesSlideeDateTimeAutoUpdate != null)
                _chkNotesSlideeDateTimeAutoUpdate.Checked = notesSlide.DateTime.AutoUpdate;

            if (_cmbNotesApplyScope != null)
            {
                _cmbNotesApplyScope.SelectedIndex = notesSlide.ApplyScope switch
                {
                    "AllNotesSlides" => 0,
                    "SlideRange" => 1,
                    "SelectedNotesSlides" => 2,
                    _ => 0
                };
            }
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        private void SaveSettings()
        {
            // 保存删除设置
            SaveDeletionSettings();

            // 保存演示文稿设置
            SavePresentationSettings();

            // 保存幻灯片设置
            SaveSlideSettings();

            // 保存母版设置
            SaveMasterSettings();

            // 保存布局设置
            SaveLayoutSettings();

            // 保存备注设置
            SaveNotesSettings();

            Settings.IsEnabled = _chkPresentationEnabled?.Checked == true ||
                                _chkSlideEnabled?.Checked == true ||
                                _chkMasterEnabled?.Checked == true ||
                                _chkLayoutEnabled?.Checked == true ||
                                _chkNotesEnabled?.Checked == true;
        }

        /// <summary>
        /// 保存删除设置
        /// </summary>
        private void SaveDeletionSettings()
        {
            var deletion = Settings.DeletionSettings;

            deletion.DeleteAllFooters = _chkDeleteFooters?.Checked ?? false;
            deletion.DeleteAllSlideNumbers = _chkDeleteSlideNumbers?.Checked ?? false;
            deletion.DeleteAllDateTimes = _chkDeleteDateTimes?.Checked ?? false;

            if (_cmbDeletionScope != null)
            {
                deletion.DeletionScope = _cmbDeletionScope.SelectedIndex switch
                {
                    0 => "All",
                    1 => "SlideRange",
                    2 => "SelectedSlides",
                    _ => "All"
                };
            }

            deletion.SlideRange = _txtSlideRange?.Text ?? "1-";
        }

        /// <summary>
        /// 保存演示文稿设置
        /// </summary>
        private void SavePresentationSettings()
        {
            var presentation = Settings.PresentationSettings;

            presentation.IsEnabled = _chkPresentationEnabled?.Checked ?? false;
            presentation.ShowFooter = _chkPresentationShowFooter?.Checked ?? false;
            presentation.FooterText = _txtPresentationFooter?.Text ?? "";

            presentation.ShowSlideNumber = _chkPresentationShowSlideNumber?.Checked ?? false;
            presentation.ShowDateTime = _chkPresentationShowDateTime?.Checked ?? false;
            presentation.DateTimeText = _txtPresentationDateTime?.Text ?? "";
            presentation.DateTimeFormat = _cmbPresentationDateTimeFormat?.Text ?? "yyyy/MM/dd";
            presentation.AutoUpdateDateTime = _chkPresentationAutoUpdateDateTime?.Checked ?? true;
        }

        /// <summary>
        /// 保存幻灯片设置
        /// </summary>
        private void SaveSlideSettings()
        {
            var slide = Settings.SlideSettings;

            slide.IsEnabled = _chkSlideEnabled?.Checked ?? false;

            // 页脚设置
            slide.Footer.IsEnabled = _chkSlideFooterEnabled?.Checked ?? false;
            slide.Footer.IsVisible = _chkSlideFooterVisible?.Checked ?? false;
            slide.Footer.Text = _txtSlideFooter?.Text ?? "";

            // 页码设置
            slide.SlideNumber.IsEnabled = _chkSlideNumberEnabled?.Checked ?? false;
            slide.SlideNumber.IsVisible = _chkSlideNumberVisible?.Checked ?? false;
            slide.SlideNumber.Format = _cmbSlideNumberFormat?.Text ?? "#";
            slide.SlideNumber.StartNumber = (int)(_nudSlideNumberStart?.Value ?? 1);

            // 日期时间设置
            slide.DateTime.IsEnabled = _chkSlideDateTimeEnabled?.Checked ?? false;
            slide.DateTime.IsVisible = _chkSlideDateTimeVisible?.Checked ?? false;
            slide.DateTime.Format = _cmbSlideDateTimeFormat?.Text ?? "yyyy/MM/dd";
            slide.DateTime.AutoUpdate = _chkSlideDateTimeAutoUpdate?.Checked ?? true;
            slide.DateTime.UseCustomText = _chkSlideDateTimeUseCustom?.Checked ?? false;
            slide.DateTime.CustomText = _txtSlideDateTimeCustom?.Text ?? "";

            // 应用范围
            if (_cmbSlideApplyScope != null)
            {
                slide.ApplyScope = _cmbSlideApplyScope.SelectedIndex switch
                {
                    0 => "AllSlides",
                    1 => "SlideRange",
                    2 => "SelectedSlides",
                    _ => "AllSlides"
                };
            }

            slide.SlideRange = _txtSlideRange?.Text ?? "1-";

            // 选中的幻灯片
            slide.SelectedSlideIndexes.Clear();
            if (_lstSelectedSlides != null)
            {
                foreach (int index in _lstSelectedSlides.SelectedIndices)
                {
                    slide.SelectedSlideIndexes.Add(index);
                }
            }
        }

        /// <summary>
        /// 保存母版设置
        /// </summary>
        private void SaveMasterSettings()
        {
            var master = Settings.MasterSlideSettings;

            master.IsEnabled = _chkMasterEnabled?.Checked ?? false;

            // 页脚设置
            master.Footer.IsEnabled = _chkMasterFooterEnabled?.Checked ?? false;
            master.Footer.IsVisible = _chkMasterFooterVisible?.Checked ?? false;
            master.Footer.Text = _txtMasterFooter?.Text ?? "";



            // 页码设置
            master.SlideNumber.IsEnabled = _chkMasterNumberEnabled?.Checked ?? false;
            master.SlideNumber.IsVisible = _chkMasterNumberVisible?.Checked ?? false;
            master.SlideNumber.Format = _cmbMasterNumberFormat?.Text ?? "#";
            master.SlideNumber.StartNumber = (int)(_nudMasterNumberStart?.Value ?? 1);

            // 日期时间设置
            master.DateTime.IsEnabled = _chkMasterDateTimeEnabled?.Checked ?? false;
            master.DateTime.IsVisible = _chkMasterDateTimeVisible?.Checked ?? false;
            master.DateTime.Format = _cmbMasterDateTimeFormat?.Text ?? "yyyy/MM/dd";
            master.DateTime.AutoUpdate = _chkMasterDateTimeAutoUpdate?.Checked ?? true;
            master.DateTime.UseCustomText = _chkMasterDateTimeUseCustom?.Checked ?? false;
            master.DateTime.CustomText = _txtMasterDateTimeCustom?.Text ?? "";

            // 应用到子幻灯片
            master.ApplyToChildSlides = _chkMasterApplyToChildren?.Checked ?? true;
        }

        /// <summary>
        /// 保存布局设置
        /// </summary>
        private void SaveLayoutSettings()
        {
            var layout = Settings.LayoutSlideSettings;

            layout.IsEnabled = _chkLayoutEnabled?.Checked ?? false;
            layout.ApplyToAllLayouts = _chkLayoutApplyToAll?.Checked ?? true;

            // 页脚设置
            layout.Footer.IsEnabled = _chkLayoutFooterEnabled?.Checked ?? false;
            layout.Footer.IsVisible = _chkLayoutFooterVisible?.Checked ?? false;
            layout.Footer.Text = _txtLayoutFooter?.Text ?? "";



            // 页码设置
            layout.SlideNumber.IsEnabled = _chkLayoutNumberEnabled?.Checked ?? false;
            layout.SlideNumber.IsVisible = _chkLayoutNumberVisible?.Checked ?? false;
            layout.SlideNumber.Format = _cmbLayoutNumberFormat?.Text ?? "#";
            layout.SlideNumber.StartNumber = (int)(_nudLayoutNumberStart?.Value ?? 1);

            // 日期时间设置
            layout.DateTime.IsEnabled = _chkLayoutDateTimeEnabled?.Checked ?? false;
            layout.DateTime.IsVisible = _chkLayoutDateTimeVisible?.Checked ?? false;
            layout.DateTime.Format = _cmbLayoutDateTimeFormat?.Text ?? "yyyy/MM/dd";
            layout.DateTime.AutoUpdate = _chkLayoutDateTimeAutoUpdate?.Checked ?? true;
            layout.DateTime.UseCustomText = _chkLayoutDateTimeUseCustom?.Checked ?? false;
            layout.DateTime.CustomText = _txtLayoutDateTimeCustom?.Text ?? "";

            // 选中的布局幻灯片
            layout.SelectedLayoutIndexes.Clear();
            if (_lstLayoutSlides != null)
            {
                foreach (int index in _lstLayoutSlides.SelectedIndices)
                {
                    layout.SelectedLayoutIndexes.Add(index);
                }
            }
        }

        /// <summary>
        /// 保存备注设置
        /// </summary>
        private void SaveNotesSettings()
        {
            var notes = Settings.NotesSlideSettings;

            notes.IsEnabled = _chkNotesEnabled?.Checked ?? false;

            // 备注母版设置
            var notesMaster = notes.NotesMaster;
            notesMaster.IsEnabled = true; // 如果备注启用，则母版也启用
            notesMaster.Footer.IsEnabled = _chkNotesMasterFooterEnabled?.Checked ?? false;
            notesMaster.Footer.IsVisible = _chkNotesMasterFooterVisible?.Checked ?? false;
            notesMaster.Footer.Text = _txtNotesMasterFooter?.Text ?? "";



            notesMaster.SlideNumber.IsEnabled = _chkNotesMasterNumberEnabled?.Checked ?? false;
            notesMaster.SlideNumber.IsVisible = _chkNotesMasterNumberVisible?.Checked ?? false;
            notesMaster.SlideNumber.Format = _cmbNotesMasterNumberFormat?.Text ?? "#";
            notesMaster.SlideNumber.StartNumber = (int)(_nudNotesMasterNumberStart?.Value ?? 1);

            notesMaster.DateTime.IsEnabled = _chkNotesMasterDateTimeEnabled?.Checked ?? false;
            notesMaster.DateTime.IsVisible = _chkNotesMasterDateTimeVisible?.Checked ?? false;
            notesMaster.DateTime.Format = _cmbNotesMasterDateTimeFormat?.Text ?? "yyyy/MM/dd";
            notesMaster.DateTime.AutoUpdate = _chkNotesMasterDateTimeAutoUpdate?.Checked ?? true;

            notesMaster.ApplyToChildSlides = _chkNotesMasterApplyToChildren?.Checked ?? true;

            // 备注幻灯片设置
            var notesSlide = notes.NotesSlide;
            notesSlide.IsEnabled = true; // 如果备注启用，则幻灯片也启用
            notesSlide.Footer.IsEnabled = _chkNotesSlideFooterEnabled?.Checked ?? false;
            notesSlide.Footer.IsVisible = _chkNotesSlideFooterVisible?.Checked ?? false;
            notesSlide.Footer.Text = _txtNotesSlideFooter?.Text ?? "";



            notesSlide.SlideNumber.IsEnabled = _chkNotesSlideNumberEnabled?.Checked ?? false;
            notesSlide.SlideNumber.IsVisible = _chkNotesSlideNumberVisible?.Checked ?? false;
            notesSlide.SlideNumber.Format = _cmbNotesSlideNumberFormat?.Text ?? "#";
            notesSlide.SlideNumber.StartNumber = (int)(_nudNotesSlideNumberStart?.Value ?? 1);

            notesSlide.DateTime.IsEnabled = _chkNotesSlideeDateTimeEnabled?.Checked ?? false;
            notesSlide.DateTime.IsVisible = _chkNotesSlideeDateTimeVisible?.Checked ?? false;
            notesSlide.DateTime.Format = _cmbNotesSlideeDateTimeFormat?.Text ?? "yyyy/MM/dd";
            notesSlide.DateTime.AutoUpdate = _chkNotesSlideeDateTimeAutoUpdate?.Checked ?? true;

            if (_cmbNotesApplyScope != null)
            {
                notesSlide.ApplyScope = _cmbNotesApplyScope.SelectedIndex switch
                {
                    0 => "AllNotesSlides",
                    1 => "SlideRange",
                    2 => "SelectedNotesSlides",
                    _ => "AllNotesSlides"
                };
            }
        }

        #endregion

        #region 控件状态更新

        /// <summary>
        /// 更新所有控件状态
        /// </summary>
        private void UpdateAllControlsState()
        {
            UpdatePresentationControlsState();
            UpdateSlideControlsState();
            UpdateMasterControlsState();
            UpdateLayoutControlsState();
            UpdateNotesControlsState();
            UpdateDeletionScopeControls();
            UpdateSlideApplyScopeControls();
            UpdateLayoutApplyControls();
        }

        /// <summary>
        /// 更新演示文稿控件状态
        /// </summary>
        private void UpdatePresentationControlsState()
        {
            var enabled = _chkPresentationEnabled?.Checked ?? false;

            // 启用/禁用相关控件
            if (_chkPresentationShowFooter != null)
                _chkPresentationShowFooter.Enabled = enabled;
            if (_txtPresentationFooter != null)
                _txtPresentationFooter.Enabled = enabled;

            if (_chkPresentationShowSlideNumber != null)
                _chkPresentationShowSlideNumber.Enabled = enabled;
            if (_chkPresentationShowDateTime != null)
                _chkPresentationShowDateTime.Enabled = enabled;
            if (_txtPresentationDateTime != null)
                _txtPresentationDateTime.Enabled = enabled;
            if (_cmbPresentationDateTimeFormat != null)
                _cmbPresentationDateTimeFormat.Enabled = enabled;
            if (_chkPresentationAutoUpdateDateTime != null)
                _chkPresentationAutoUpdateDateTime.Enabled = enabled;
        }

        /// <summary>
        /// 更新幻灯片控件状态
        /// </summary>
        private void UpdateSlideControlsState()
        {
            var enabled = _chkSlideEnabled?.Checked ?? false;

            // 启用/禁用组框
            if (_grpSlideFooter != null)
                _grpSlideFooter.Enabled = enabled;

            if (_grpSlideNumber != null)
                _grpSlideNumber.Enabled = enabled;
            if (_grpSlideDateTime != null)
                _grpSlideDateTime.Enabled = enabled;
            if (_cmbSlideApplyScope != null)
                _cmbSlideApplyScope.Enabled = enabled;
        }

        /// <summary>
        /// 更新母版控件状态
        /// </summary>
        private void UpdateMasterControlsState()
        {
            var enabled = _chkMasterEnabled?.Checked ?? false;

            // 启用/禁用组框
            if (_grpMasterFooter != null)
                _grpMasterFooter.Enabled = enabled;

            if (_grpMasterNumber != null)
                _grpMasterNumber.Enabled = enabled;
            if (_grpMasterDateTime != null)
                _grpMasterDateTime.Enabled = enabled;
            if (_chkMasterApplyToChildren != null)
                _chkMasterApplyToChildren.Enabled = enabled;
        }

        /// <summary>
        /// 更新布局控件状态
        /// </summary>
        private void UpdateLayoutControlsState()
        {
            var enabled = _chkLayoutEnabled?.Checked ?? false;

            // 启用/禁用组框
            if (_grpLayoutFooter != null)
                _grpLayoutFooter.Enabled = enabled;

            if (_grpLayoutNumber != null)
                _grpLayoutNumber.Enabled = enabled;
            if (_grpLayoutDateTime != null)
                _grpLayoutDateTime.Enabled = enabled;
            if (_chkLayoutApplyToAll != null)
                _chkLayoutApplyToAll.Enabled = enabled;
            if (_lstLayoutSlides != null)
                _lstLayoutSlides.Enabled = enabled;
        }

        /// <summary>
        /// 更新备注控件状态
        /// </summary>
        private void UpdateNotesControlsState()
        {
            var enabled = _chkNotesEnabled?.Checked ?? false;

            // 启用/禁用选项卡控件
            if (_tabNotesSettings != null)
                _tabNotesSettings.Enabled = enabled;
        }

        /// <summary>
        /// 更新删除范围控件状态
        /// </summary>
        private void UpdateDeletionScopeControls()
        {
            if (_cmbDeletionScope == null || _txtSlideRange == null)
                return;

            var selectedIndex = _cmbDeletionScope.SelectedIndex;
            _txtSlideRange.Enabled = selectedIndex == 1; // 指定范围
        }

        /// <summary>
        /// 更新幻灯片应用范围控件状态
        /// </summary>
        private void UpdateSlideApplyScopeControls()
        {
            if (_cmbSlideApplyScope == null || _txtSlideRange == null || _lstSelectedSlides == null)
                return;

            var selectedIndex = _cmbSlideApplyScope.SelectedIndex;
            _txtSlideRange.Enabled = selectedIndex == 1; // 指定范围
            _lstSelectedSlides.Enabled = selectedIndex == 2; // 选中幻灯片
        }

        /// <summary>
        /// 更新布局应用控件状态
        /// </summary>
        private void UpdateLayoutApplyControls()
        {
            if (_chkLayoutApplyToAll == null || _lstLayoutSlides == null)
                return;

            var applyToAll = _chkLayoutApplyToAll.Checked;
            _lstLayoutSlides.Enabled = !applyToAll;
        }



        /// <summary>
        /// 幻灯片日期时间自定义文本选择变更事件
        /// </summary>
        private void ChkSlideDateTimeUseCustom_CheckedChanged(object? sender, EventArgs e)
        {
            if (_txtSlideDateTimeCustom != null && _chkSlideDateTimeUseCustom != null)
                _txtSlideDateTimeCustom.Enabled = _chkSlideDateTimeUseCustom.Checked;
        }

        /// <summary>
        /// 母版日期时间自定义文本选择变更事件
        /// </summary>
        private void ChkMasterDateTimeUseCustom_CheckedChanged(object? sender, EventArgs e)
        {
            if (_txtMasterDateTimeCustom != null && _chkMasterDateTimeUseCustom != null)
                _txtMasterDateTimeCustom.Enabled = _chkMasterDateTimeUseCustom.Checked;
        }

        /// <summary>
        /// 布局日期时间自定义文本选择变更事件
        /// </summary>
        private void ChkLayoutDateTimeUseCustom_CheckedChanged(object? sender, EventArgs e)
        {
            if (_txtLayoutDateTimeCustom != null && _chkLayoutDateTimeUseCustom != null)
                _txtLayoutDateTimeCustom.Enabled = _chkLayoutDateTimeUseCustom.Checked;
        }

        /// <summary>
        /// 备注母版日期时间自定义文本选择变更事件
        /// </summary>
        private void ChkNotesMasterDateTimeUseCustom_CheckedChanged(object? sender, EventArgs e)
        {
            if (_txtNotesMasterDateTimeCustom != null && _chkNotesMasterDateTimeUseCustom != null)
                _txtNotesMasterDateTimeCustom.Enabled = _chkNotesMasterDateTimeUseCustom.Checked;
        }

        /// <summary>
        /// 备注幻灯片日期时间自定义文本选择变更事件
        /// </summary>
        private void ChkNotesSlideeDateTimeUseCustom_CheckedChanged(object? sender, EventArgs e)
        {
            if (_txtNotesSlideeDateTimeCustom != null && _chkNotesSlideeDateTimeUseCustom != null)
                _txtNotesSlideeDateTimeCustom.Enabled = _chkNotesSlideeDateTimeUseCustom.Checked;
        }

        /// <summary>
        /// 备注应用范围变更事件
        /// </summary>
        private void CmbNotesApplyScope_SelectedIndexChanged(object? sender, EventArgs e)
        {
            UpdateNotesApplyScopeControls();
        }

        /// <summary>
        /// 更新备注应用范围控件状态
        /// </summary>
        private void UpdateNotesApplyScopeControls()
        {
            if (_cmbNotesApplyScope == null || _txtNotesSlideRange == null || _lstSelectedNotesSlides == null)
                return;

            var selectedIndex = _cmbNotesApplyScope.SelectedIndex;
            _txtNotesSlideRange.Enabled = selectedIndex == 1; // 指定范围
            _lstSelectedNotesSlides.Enabled = selectedIndex == 2; // 选中备注幻灯片
        }

        #endregion
    }
}

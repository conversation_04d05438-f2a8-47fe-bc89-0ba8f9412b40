using System;
using System.Drawing;
using System.Windows.Forms;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 段落格式匹配规则编辑窗体 - 事件处理部分，处理各种控件的交互事件和数据验证
    /// </summary>
    public partial class ParagraphFormatRuleEditForm
    {
        #region 段落格式事件处理器

        /// <summary>
        /// 启用段落格式设置复选框变更事件
        /// </summary>
        private void ChkEnableParagraphFormat_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 启用缩进设置复选框变更事件
        /// </summary>
        private void ChkEnableIndentation_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 启用间距设置复选框变更事件
        /// </summary>
        private void ChkEnableSpacing_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 启用中文控制选项复选框变更事件
        /// </summary>
        private void ChkEnableChineseControl_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 行距类型下拉框选择变更事件
        /// </summary>
        private void CmbLineSpacingType_SelectedIndexChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        #endregion

        #region 字体格式事件处理器

        /// <summary>
        /// 启用字体格式设置复选框变更事件
        /// </summary>
        private void ChkEnableFontFormat_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 设置中文字体复选框变更事件
        /// </summary>
        private void ChkSetChineseFont_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 设置西文字体复选框变更事件
        /// </summary>
        private void ChkSetLatinFont_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 设置字体大小复选框变更事件
        /// </summary>
        private void ChkSetFontSize_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 设置字体颜色复选框变更事件
        /// </summary>
        private void ChkSetFontColor_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 设置下划线复选框变更事件
        /// </summary>
        private void ChkSetUnderline_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 设置文字效果复选框变更事件
        /// </summary>
        private void ChkSetTextEffects_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateControlStates();
        }

        /// <summary>
        /// 字体颜色选择按钮点击事件
        /// </summary>
        private void BtnFontColor_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog
            {
                Color = _btnFontColor.BackColor,
                FullOpen = true
            };

            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                _btnFontColor.BackColor = colorDialog.Color;
                _btnFontColor.ForeColor = GetContrastColor(colorDialog.Color);
            }
        }

        /// <summary>
        /// 下划线颜色选择按钮点击事件
        /// </summary>
        private void BtnUnderlineColor_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog
            {
                Color = _btnUnderlineColor.BackColor,
                FullOpen = true
            };

            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                _btnUnderlineColor.BackColor = colorDialog.Color;
                _btnUnderlineColor.ForeColor = GetContrastColor(colorDialog.Color);
            }
        }

        /// <summary>
        /// 获取对比色
        /// </summary>
        private Color GetContrastColor(Color color)
        {
            // 计算亮度
            var brightness = (color.R * 299 + color.G * 587 + color.B * 114) / 1000;
            return brightness > 128 ? Color.Black : Color.White;
        }

        #endregion

        #region 按钮事件处理器

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            if (ValidateInput())
            {
                SaveRuleData();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 应用按钮点击事件
        /// </summary>
        private void BtnApply_Click(object? sender, EventArgs e)
        {
            if (ValidateInput())
            {
                SaveRuleData();
                // 移除不必要的确认提示框
            }
        }

        #endregion

        #region 输入验证

        /// <summary>
        /// 验证输入
        /// </summary>
        private bool ValidateInput()
        {
            // 验证规则名称
            if (string.IsNullOrWhiteSpace(_txtRuleName.Text))
            {
                MessageBox.Show("请输入规则名称", "输入验证", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _txtRuleName.Focus();
                return false;
            }

            // 验证匹配条件
            if (!ValidateMatchingConditions())
            {
                return false;
            }

            // 验证正则表达式
            if (_chkRegex.Checked && !string.IsNullOrWhiteSpace(_txtRegexPattern.Text))
            {
                try
                {
                    System.Text.RegularExpressions.Regex.IsMatch("test", _txtRegexPattern.Text);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"正则表达式格式错误: {ex.Message}", "输入验证", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    _tabControl.SelectedIndex = 0; // 切换到匹配条件标签页
                    _txtRegexPattern.Focus();
                    return false;
                }
            }

            // 验证字符数限制
            if (_chkCharacterCount.Checked)
            {
                if (_numMinCharCount.Value > _numMaxCharCount.Value)
                {
                    MessageBox.Show("最小字符数不能大于最大字符数", "输入验证", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    _tabControl.SelectedIndex = 0; // 切换到匹配条件标签页
                    _numMinCharCount.Focus();
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 验证匹配条件
        /// </summary>
        private bool ValidateMatchingConditions()
        {
            // 检查是否至少启用了一个匹配条件
            bool hasCondition = _chkStartsWith.Checked && !string.IsNullOrWhiteSpace(_txtStartsWith.Text) ||
                               _chkContains.Checked && _listContainsKeywords.Items.Count > 0 ||
                               _chkEndsWith.Checked && !string.IsNullOrWhiteSpace(_txtEndsWith.Text) ||
                               _chkRegex.Checked && !string.IsNullOrWhiteSpace(_txtRegexPattern.Text) ||
                               _chkCharacterCount.Checked;

            if (!hasCondition)
            {
                MessageBox.Show("请至少设置一个匹配条件", "输入验证", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _tabControl.SelectedIndex = 0; // 切换到匹配条件标签页
                return false;
            }

            return true;
        }

        #endregion
    }
}

/// <summary>
/// PPT格式转换设置窗体 - 提供PPT文件转换为PDF、图片、Web格式等功能的配置界面
/// 支持批量转换、格式选择、质量设置、布局选项等高级功能
/// 符合Aspose.Slides API规范，提供完整的格式转换解决方案
/// 功能包括：PDF转换、图片转换(PNG/JPEG/BMP/TIFF/SVG)、HTML转换、XAML转换、缩略图生成
/// </summary>

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// PPT格式转换设置窗体 - 提供PPT文件转换为PDF、图片、Web格式等功能的配置界面
    /// 支持批量转换、格式选择、质量设置、布局选项等高级功能
    /// 符合Aspose.Slides API规范，提供完整的格式转换解决方案
    /// </summary>
    public partial class PPTFormatConversionForm : Form
    {
        #region 字段和属性

        // 当前设置
        private PPTFormatConversionSettings _currentSettings = new();

        // 各标签页的控件容器
        private readonly Dictionary<string, Panel> _tabPanels = new();



        #endregion

        #region 构造函数

        public PPTFormatConversionForm()
        {
            InitializeComponent();
            InitializeSettings();
            InitializeCustomControls();
            SetupEventHandlers();
        }

        public PPTFormatConversionForm(PPTFormatConversionSettings settings) : this()
        {
            _currentSettings = settings ?? new PPTFormatConversionSettings();
            LoadSettingsToUI();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 获取当前设置
        /// </summary>
        public PPTFormatConversionSettings GetCurrentSettings()
        {
            SaveUIToSettings();
            return _currentSettings;
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化设置
        /// </summary>
        private void InitializeSettings()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();
                _currentSettings = config.PPTFormatConversionSettings ?? new PPTFormatConversionSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载PPT格式转换设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                _currentSettings = new PPTFormatConversionSettings();
            }
        }

        /// <summary>
        /// 初始化自定义控件
        /// </summary>
        private void InitializeCustomControls()
        {
            try
            {
                // 设置窗体属性
                SetupFormProperties();

                // 初始化标签页内容
                InitializePDFTab();
                InitializeImageTab();
                InitializeWebTab();
                InitializeOtherTab();

                // 设置标签页宽度均匀分布
                SetupTabWidths();

                // 控件变化监听已简化，无需额外的修改状态管理
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化控件时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置窗体属性 - 宽松布局设计，确保所有内容完整显示
        /// </summary>
        private void SetupFormProperties()
        {
            this.Text = "PPT格式转换设置";
            this.Size = new Size(1300, 1000);  // 增大窗体尺寸，确保宽松布局
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;  // 允许调整大小
            this.MaximizeBox = true;  // 允许最大化
            this.MinimizeBox = true;  // 允许最小化
            this.MinimumSize = new Size(1200, 900);  // 设置最小尺寸
            this.Font = new Font("Microsoft YaHei UI", 10F);  // 基础字体10F
            this.BackColor = Color.FromArgb(248, 248, 248);  // 浅灰背景色
        }

        /// <summary>
        /// 设置标签页宽度均匀分布 - 宽松布局设计
        /// </summary>
        private void SetupTabWidths()
        {
            if (tabControlMain.TabPages.Count > 0)
            {
                int totalWidth = tabControlMain.Width;
                int tabWidth = totalWidth / tabControlMain.TabPages.Count;

                // 设置每个标签页的宽度和高度 - 增加高度确保文字完整显示
                tabControlMain.SizeMode = TabSizeMode.Fixed;
                tabControlMain.ItemSize = new Size(tabWidth - 6, 45);  // 标签页高度45，确保文字完整显示
                tabControlMain.Font = new Font("Microsoft YaHei UI", 11F);  // 标签页字体11F
                tabControlMain.Padding = new Point(8, 8);  // 增加标签页内边距
            }
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 按钮事件
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            btnApply.Click += BtnApply_Click;
            btnReset.Click += BtnReset_Click;

            // 窗体事件
            this.FormClosing += PPTFormatConversionForm_FormClosing;
            this.Resize += PPTFormatConversionForm_Resize;
        }

        #endregion

        #region 标签页初始化方法

        /// <summary>
        /// 初始化转PDF标签页 - 宽松布局设计
        /// </summary>
        private void InitializePDFTab()
        {
            var panel = CreateScrollablePanel();
            tabPagePDF.Controls.Add(panel);
            _tabPanels["PDF"] = panel;

            int yPos = 25;  // 起始位置

            // 总开关 - 突出显示，控制PDF转换功能的启用状态
            var chkMasterSwitch = CreateCheckBox("启用PDF转换功能", 25, yPos, 450);
            chkMasterSwitch.Name = "chkPDFMaster";
            chkMasterSwitch.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);  // 主开关字体12F加粗
            chkMasterSwitch.ForeColor = Color.FromArgb(0, 120, 215);  // 蓝色主开关
            chkMasterSwitch.Size = new Size(450, 45);  // 增大主开关尺寸
            // 功能开关：控制PDF转换功能的启用/禁用，勾选后可配置PDF转换相关参数
            chkMasterSwitch.CheckedChanged += (s, e) => {
                SetPDFControlsEnabled(panel, chkMasterSwitch.Checked);
            };
            panel.Controls.Add(chkMasterSwitch);
            yPos += 65;  // 增大间距

            // PDF转换基本设置
            CreatePDFBasicSettings(panel, ref yPos);

            // 转换选项
            CreatePDFConversionOptions(panel, ref yPos);

            // 布局选项
            CreatePDFLayoutOptions(panel, ref yPos);

            // 高级选项
            CreatePDFAdvancedOptions(panel, ref yPos);
        }

        /// <summary>
        /// 初始化转图片标签页 - 宽松布局设计
        /// </summary>
        private void InitializeImageTab()
        {
            var panel = CreateScrollablePanel();
            tabPageImage.Controls.Add(panel);
            _tabPanels["Image"] = panel;

            int yPos = 25;  // 起始位置

            // 总开关 - 突出显示，控制图片转换功能的启用状态
            var chkMasterSwitch = CreateCheckBox("启用图片转换功能", 25, yPos, 450);
            chkMasterSwitch.Name = "chkImageMaster";
            chkMasterSwitch.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);  // 主开关字体12F加粗
            chkMasterSwitch.ForeColor = Color.FromArgb(0, 120, 215);  // 蓝色主开关
            chkMasterSwitch.Size = new Size(450, 45);  // 增大主开关尺寸
            // 功能开关：控制图片转换功能的启用/禁用，勾选后可配置PNG/JPEG/BMP/TIFF/SVG等格式转换参数
            chkMasterSwitch.CheckedChanged += (s, e) => {
                SetImageControlsEnabled(panel, chkMasterSwitch.Checked);
            };
            panel.Controls.Add(chkMasterSwitch);
            yPos += 65;  // 增大间距

            // 图片格式选择
            CreateImageFormatSelection(panel, ref yPos);

            // 图片质量设置
            CreateImageQualitySettings(panel, ref yPos);

            // 输出选项
            CreateImageOutputOptions(panel, ref yPos);
        }

        /// <summary>
        /// 初始化转Web标签页 - 宽松布局设计
        /// </summary>
        private void InitializeWebTab()
        {
            var panel = CreateScrollablePanel();
            tabPageWeb.Controls.Add(panel);
            _tabPanels["Web"] = panel;

            int yPos = 25;  // 起始位置

            // 总开关 - 突出显示，控制Web转换功能的启用状态
            var chkMasterSwitch = CreateCheckBox("启用Web转换功能", 25, yPos, 450);
            chkMasterSwitch.Name = "chkWebMaster";
            chkMasterSwitch.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);  // 主开关字体12F加粗
            chkMasterSwitch.ForeColor = Color.FromArgb(0, 120, 215);  // 蓝色主开关
            chkMasterSwitch.Size = new Size(450, 45);  // 增大主开关尺寸
            // 功能开关：控制Web转换功能的启用/禁用，勾选后可配置HTML和XAML转换参数
            chkMasterSwitch.CheckedChanged += (s, e) => {
                SetWebControlsEnabled(panel, chkMasterSwitch.Checked);
            };
            panel.Controls.Add(chkMasterSwitch);
            yPos += 65;  // 增大间距

            // HTML转换设置
            CreateHTMLConversionSettings(panel, ref yPos);

            // XAML转换设置
            CreateXAMLConversionSettings(panel, ref yPos);
        }

        /// <summary>
        /// 初始化转其他标签页
        /// </summary>
        private void InitializeOtherTab()
        {
            var panel = CreateScrollablePanel();
            tabPageOther.Controls.Add(panel);
            _tabPanels["Other"] = panel;

            int yPos = 25;  // 起始位置

            // 总开关 - 突出显示，控制其他格式转换功能的启用状态
            var chkMasterSwitch = CreateCheckBox("启用其他格式转换功能", 25, yPos, 450);
            chkMasterSwitch.Name = "chkOtherMaster";
            chkMasterSwitch.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);  // 主开关字体12F加粗
            chkMasterSwitch.ForeColor = Color.FromArgb(0, 120, 215);  // 蓝色主开关
            chkMasterSwitch.Size = new Size(450, 45);  // 增大主开关尺寸
            // 功能开关：控制其他格式转换功能的启用/禁用，勾选后可配置缩略图生成等参数
            chkMasterSwitch.CheckedChanged += (s, e) => {
                SetOtherControlsEnabled(panel, chkMasterSwitch.Checked);
            };
            panel.Controls.Add(chkMasterSwitch);
            yPos += 65;  // 增大间距

            // 缩略图生成设置
            CreateThumbnailSettings(panel, ref yPos);
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 创建可滚动面板 - 宽松布局设计
        /// </summary>
        private Panel CreateScrollablePanel()
        {
            return new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,  // 自动滚动，内容超出时显示滚动条
                BackColor = Color.FromArgb(254, 254, 254),  // 纯白背景色
                Padding = new Padding(30, 25, 30, 25),  // 增大内边距，左右30，上下25
                AutoScrollMinSize = new Size(0, 1200),  // 增大最小滚动高度，适应宽松布局
                AutoScrollMargin = new Size(10, 10)  // 滚动边距
            };
        }

        /// <summary>
        /// 创建复选框 - 宽松布局设计，确保行高不低于40
        /// </summary>
        private CheckBox CreateCheckBox(string text, int x, int y, int width = 400)
        {
            return new CheckBox
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, 42),  // 行高42，确保不低于40
                Font = new Font("Microsoft YaHei UI", 10F),
                UseVisualStyleBackColor = true,
                TextAlign = ContentAlignment.MiddleLeft,  // 垂直居中对齐
                Padding = new Padding(5, 0, 0, 0),  // 左侧间距5
                ForeColor = Color.FromArgb(50, 50, 50)  // 深灰色文字
            };
        }

        /// <summary>
        /// 创建分组框 - 宽松布局设计，增加内边距和间距
        /// </summary>
        private GroupBox CreateGroupBox(string text, int x, int y, int width, int height)
        {
            return new GroupBox
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, height),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold),  // 分组框标题字体11F
                BackColor = Color.FromArgb(250, 250, 250),  // 浅灰背景
                ForeColor = Color.FromArgb(40, 40, 40),  // 深灰色标题
                Padding = new Padding(15, 12, 15, 12),  // 增大内边距
                FlatStyle = FlatStyle.Flat  // 扁平样式
            };
        }

        /// <summary>
        /// 设置下拉框文字居中
        /// </summary>
        private void SetComboBoxTextCenter(ComboBox comboBox)
        {
            comboBox.DrawMode = DrawMode.OwnerDrawFixed;
            comboBox.DrawItem += (sender, e) =>
            {
                if (e.Index < 0) return;

                e.DrawBackground();

                var text = comboBox.Items[e.Index].ToString();
                var textBounds = e.Bounds;
                var textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter;

                // 修复编译警告：确保字体不为null
                var font = e.Font ?? comboBox.Font ?? SystemFonts.DefaultFont;
                TextRenderer.DrawText(e.Graphics, text, font, textBounds, e.ForeColor, textFlags);

                e.DrawFocusRectangle();
            };
        }

        #endregion

        #region PDF转换设置创建方法

        /// <summary>
        /// 创建PDF转换基本设置 - 宽松布局设计
        /// </summary>
        private void CreatePDFBasicSettings(Panel parent, ref int yPos)
        {
            var grpBasic = CreateGroupBox("PDF转换基本设置", 25, yPos, 1120, 160);  // 增大宽度和高度

            // 第一行：批量转换
            var chkBatchConversion = CreateCheckBox("批量转换", 35, 45, 350);
            chkBatchConversion.Name = "chkPDFBatchConversion";

            // 第二行：保持原始格式
            var chkKeepFormat = CreateCheckBox("保持原始格式", 35, 90, 350);  // 增大行距到45
            chkKeepFormat.Name = "chkPDFKeepFormat";

            grpBasic.Controls.Add(chkBatchConversion);
            grpBasic.Controls.Add(chkKeepFormat);

            parent.Controls.Add(grpBasic);
            yPos += 185;  // 增大间距
        }

        /// <summary>
        /// 创建PDF转换选项 - 宽松布局设计
        /// </summary>
        private void CreatePDFConversionOptions(Panel parent, ref int yPos)
        {
            var grpOptions = CreateGroupBox("转换选项", 25, yPos, 1120, 280);  // 增大宽度和高度

            // 第一行：页面范围选择
            var chkPageRange = CreateCheckBox("指定页面范围", 35, 45, 400);
            chkPageRange.Name = "chkPDFPageRange";

            // 第二行：起始页和结束页 - 水平对齐
            var lblStartPage = new Label
            {
                Text = "起始页:",
                Location = new Point(25, 93),  // 与复选框对齐，增大行距
                Size = new Size(80, 42),  // 增大高度确保垂直居中
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            var numStartPage = new NumericUpDown
            {
                Name = "numPDFStartPage",
                Location = new Point(110, 95),  // 精确对齐
                Size = new Size(100, 38),  // 增大尺寸
                Font = new Font("Microsoft YaHei UI", 10F),
                Minimum = 1,
                Maximum = 9999,
                Value = 1,
                TextAlign = HorizontalAlignment.Center
            };

            var lblEndPage = new Label
            {
                Text = "结束页:",
                Location = new Point(300, 93),  // 与起始页标签对齐
                Size = new Size(80, 42),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            var numEndPage = new NumericUpDown
            {
                Name = "numPDFEndPage",
                Location = new Point(390, 95),  // 精确对齐
                Size = new Size(100, 38),
                Font = new Font("Microsoft YaHei UI", 10F),
                Minimum = 1,
                Maximum = 9999,
                Value = 1,
                TextAlign = HorizontalAlignment.Center
            };

            // 第三行：图片质量设置 - 水平对齐
            var lblImageQuality = new Label
            {
                Text = "图片质量:",
                Location = new Point(25, 143),  // 与第一行对齐，增大行距
                Size = new Size(100, 42),  // 增大高度确保垂直居中
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            var numImageQuality = new NumericUpDown
            {
                Name = "numPDFImageQuality",
                Location = new Point(140, 145),  // 与上面数字框对齐
                Size = new Size(100, 38),
                Font = new Font("Microsoft YaHei UI", 10F),
                Minimum = 1,
                Maximum = 100,
                Value = 90,
                TextAlign = HorizontalAlignment.Center
            };

            var lblQualityPercent = new Label
            {
                Text = "%",
                Location = new Point(250, 143),  // 与标签对齐
                Size = new Size(30, 42),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            // 压缩选项 - 同一行
            var lblCompression = new Label
            {
                Text = "压缩级别:",
                Location = new Point(300, 143),  // 与图片质量同一行
                Size = new Size(100, 42),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            var cmbCompression = new ComboBox
            {
                Name = "cmbPDFCompression",
                Location = new Point(405, 145),  // 与数字框对齐
                Size = new Size(140, 38),
                Font = new Font("Microsoft YaHei UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbCompression.Items.AddRange(new string[] { "无压缩", "标准", "高压缩" });
            cmbCompression.SelectedIndex = 1;
            // 设置下拉框文字居中
            SetComboBoxTextCenter(cmbCompression);

            // 第四行：密码保护 - 水平对齐
            var chkPassword = CreateCheckBox("密码保护", 35, 195, 350);  // 增大行距
            chkPassword.Name = "chkPDFPassword";

            var txtPassword = new TextBox
            {
                Name = "txtPDFPassword",
                Location = new Point(200, 200),  // 与复选框对齐
                Size = new Size(200, 35),  // 增大尺寸
                Font = new Font("Microsoft YaHei UI", 10F),
                UseSystemPasswordChar = true,
                TextAlign = HorizontalAlignment.Center,
                Enabled = false,
                BackColor = Color.FromArgb(248, 248, 248)  // 浅灰背景
            };

            // 密码保护复选框事件
            chkPassword.CheckedChanged += (s, e) =>
            {
                txtPassword.Enabled = chkPassword.Checked;
                txtPassword.BackColor = chkPassword.Checked ? Color.White : Color.FromArgb(248, 248, 248);
                if (!chkPassword.Checked)
                    txtPassword.Text = "";
            };

            grpOptions.Controls.AddRange(new Control[] {
                chkPageRange, lblStartPage, numStartPage, lblEndPage, numEndPage,
                lblImageQuality, numImageQuality, lblQualityPercent,
                lblCompression, cmbCompression, chkPassword, txtPassword
            });

            parent.Controls.Add(grpOptions);
            yPos += 305;  // 增大间距
        }

        /// <summary>
        /// 创建PDF布局选项
        /// </summary>
        private void CreatePDFLayoutOptions(Panel parent, ref int yPos)
        {
            var grpLayout = CreateGroupBox("布局选项", 20, yPos, 1120, 220);  // 调整宽度适应窗体

            // 幻灯片布局选择
            var lblLayout = new Label
            {
                Text = "布局类型:",
                Location = new Point(25, 40),  // 调整位置
                Size = new Size(90, 30),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var cmbLayout = new ComboBox
            {
                Name = "cmbPDFLayout",
                Location = new Point(120, 37),
                Size = new Size(160, 30),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbLayout.Items.AddRange(new string[] { "幻灯片布局", "讲义布局", "备注布局", "大纲布局" });
            cmbLayout.SelectedIndex = 0;
            // 设置下拉框文字居中
            SetComboBoxTextCenter(cmbLayout);

            // 讲义选项
            var chkHandout = CreateCheckBox("讲义多张幻灯片", 25, 80, 250);  // 增加行距和宽度
            chkHandout.Name = "chkPDFHandout";

            var lblSlidesPerPage = new Label
            {
                Text = "每页幻灯片数:",
                Location = new Point(25, 150),  // 增加行距
                Size = new Size(110, 30),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var cmbSlidesPerPage = new ComboBox
            {
                Name = "cmbPDFSlidesPerPage",
                Location = new Point(160, 150),
                Size = new Size(90, 30),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Enabled = false
            };
            cmbSlidesPerPage.Items.AddRange(new string[] { "2", "3", "4", "6", "9" });
            cmbSlidesPerPage.SelectedIndex = 3; // 默认6张
            // 设置下拉框文字居中
            SetComboBoxTextCenter(cmbSlidesPerPage);

            // 备注选项
            var chkNotes = CreateCheckBox("包含备注", 340, 80, 300);  // 调整位置和宽度
            chkNotes.Name = "chkPDFNotes";

            var lblNotesPosition = new Label
            {
                Text = "备注位置:",
                Location = new Point(340, 150),  // 调整位置
                Size = new Size(130, 30),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var cmbNotesPosition = new ComboBox
            {
                Name = "cmbPDFNotesPosition",
                Location = new Point(475, 150),
                Size = new Size(110, 30),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Enabled = false
            };
            cmbNotesPosition.Items.AddRange(new string[] { "顶部", "底部", "左侧", "右侧" });
            cmbNotesPosition.SelectedIndex = 1; // 默认底部
            // 设置下拉框文字居中
            SetComboBoxTextCenter(cmbNotesPosition);

            // 事件处理
            chkHandout.CheckedChanged += (s, e) =>
            {
                cmbSlidesPerPage.Enabled = chkHandout.Checked;
            };

            chkNotes.CheckedChanged += (s, e) =>
            {
                cmbNotesPosition.Enabled = chkNotes.Checked;
            };

            grpLayout.Controls.AddRange(new Control[] {
                lblLayout, cmbLayout, chkHandout, lblSlidesPerPage, cmbSlidesPerPage,
                chkNotes, lblNotesPosition, cmbNotesPosition
            });

            parent.Controls.Add(grpLayout);
            yPos += 240;  // 增加间距
        }

        /// <summary>
        /// 创建PDF高级选项
        /// </summary>
        private void CreatePDFAdvancedOptions(Panel parent, ref int yPos)
        {
            var grpAdvanced = CreateGroupBox("高级选项", 20, yPos, 1120, 180);  // 调整宽度适应窗体

            var chkEmbedFonts = CreateCheckBox("嵌入字体", 25, 35, 300);  // 调整位置和宽度
            chkEmbedFonts.Name = "chkPDFEmbedFonts";
            chkEmbedFonts.Checked = true;

            var chkCompressImages = CreateCheckBox("压缩图片", 25, 70, 300);  // 增加行距
            chkCompressImages.Name = "chkPDFCompressImages";
            chkCompressImages.Checked = true;

            var chkPreserveMetadata = CreateCheckBox("保留元数据", 25, 105, 300);  // 增加行距
            chkPreserveMetadata.Name = "chkPDFPreserveMetadata";
            chkPreserveMetadata.Checked = true;

            var chkBookmarks = CreateCheckBox("生成书签", 350, 35, 300);  // 调整位置和宽度
            chkBookmarks.Name = "chkPDFBookmarks";

            var lblCompliance = new Label
            {
                Text = "PDF标准:",
                Location = new Point(350, 105),  // 调整位置
                Size = new Size(90, 30),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var cmbCompliance = new ComboBox
            {
                Name = "cmbPDFCompliance",
                Location = new Point(445, 105),
                Size = new Size(160, 30),  // 增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbCompliance.Items.AddRange(new string[] { "PDF 1.4", "PDF 1.5", "PDF/A-1a", "PDF/A-1b" });
            cmbCompliance.SelectedIndex = 1; // 默认PDF 1.5
            // 设置下拉框文字居中
            SetComboBoxTextCenter(cmbCompliance);

            grpAdvanced.Controls.AddRange(new Control[] {
                chkEmbedFonts, chkCompressImages, chkPreserveMetadata,
                chkBookmarks, lblCompliance, cmbCompliance
            });

            parent.Controls.Add(grpAdvanced);
            yPos += 200;  // 增加间距
        }

        #endregion

        #region 图片转换设置创建方法

        /// <summary>
        /// 创建图片格式选择 - 宽松布局设计
        /// </summary>
        private void CreateImageFormatSelection(Panel parent, ref int yPos)
        {
            var grpFormats = CreateGroupBox("图片格式选择", 25, yPos, 1120, 160);  // 增大宽度和高度

            // 第一行：PNG、JPEG、BMP
            var chkPNG = CreateCheckBox("PPT转PNG", 35, 45, 280);  // 增大宽度
            chkPNG.Name = "chkImagePNG";

            var chkJPEG = CreateCheckBox("PPT转JPEG", 330, 45, 280);  // 调整位置和宽度
            chkJPEG.Name = "chkImageJPEG";

            var chkBMP = CreateCheckBox("PPT转BMP", 625, 45, 280);  // 调整位置和宽度
            chkBMP.Name = "chkImageBMP";

            // 第二行：TIFF、SVG
            var chkTIFF = CreateCheckBox("PPT转TIFF", 35, 90, 280);  // 增大行距到45
            chkTIFF.Name = "chkImageTIFF";

            var chkSVG = CreateCheckBox("PPT转SVG", 330, 90, 280);  // 增大行距到45
            chkSVG.Name = "chkImageSVG";

            grpFormats.Controls.AddRange(new Control[] {
                chkPNG, chkJPEG, chkBMP, chkTIFF, chkSVG
            });

            parent.Controls.Add(grpFormats);
            yPos += 185;  // 增大间距
        }

        /// <summary>
        /// 创建图片质量设置 - 宽松布局设计
        /// </summary>
        private void CreateImageQualitySettings(Panel parent, ref int yPos)
        {
            var grpQuality = CreateGroupBox("图片质量设置", 25, yPos, 1120, 180);  // 增大宽度和高度

            // 第一行：图片尺寸设置 - 水平对齐
            var lblWidth = new Label
            {
                Text = "图片宽度:",
                Location = new Point(35, 48),  // 调整位置，确保垂直居中
                Size = new Size(100, 42),  // 增大高度确保垂直居中
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            var numWidth = new NumericUpDown
            {
                Name = "numImageWidth",
                Location = new Point(140, 50),  // 精确对齐
                Size = new Size(120, 38),  // 增大尺寸
                Font = new Font("Microsoft YaHei UI", 10F),
                Minimum = 100,
                Maximum = 9999,
                Value = 1920,
                TextAlign = HorizontalAlignment.Center
            };

            var lblHeight = new Label
            {
                Text = "图片高度:",
                Location = new Point(280, 48),  // 与宽度标签对齐
                Size = new Size(100, 42),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            var numHeight = new NumericUpDown
            {
                Name = "numImageHeight",
                Location = new Point(385, 50),  // 精确对齐
                Size = new Size(120, 38),
                Font = new Font("Microsoft YaHei UI", 10F),
                Minimum = 100,
                Maximum = 9999,
                Value = 1080,
                TextAlign = HorizontalAlignment.Center
            };

            var chkAspectRatio = CreateCheckBox("保持宽高比", 525, 50, 320);  // 调整位置和宽度
            chkAspectRatio.Name = "chkImageAspectRatio";
            chkAspectRatio.Checked = true;

            // 第二行：DPI和JPEG质量设置 - 水平对齐
            var lblDPI = new Label
            {
                Text = "DPI:",
                Location = new Point(35, 98),  // 增大行距，与第一行对齐
                Size = new Size(70, 42),  // 增大高度确保垂直居中
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            var numDPI = new NumericUpDown
            {
                Name = "numImageDPI",
                Location = new Point(110, 100),  // 精确对齐
                Size = new Size(100, 38),
                Font = new Font("Microsoft YaHei UI", 10F),
                Minimum = 72,
                Maximum = 600,
                Value = 300,
                TextAlign = HorizontalAlignment.Center
            };

            // JPEG质量设置 - 同一行
            var lblJPEGQuality = new Label
            {
                Text = "JPEG质量:",
                Location = new Point(230, 98),  // 与DPI标签对齐
                Size = new Size(100, 42),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            var numJPEGQuality = new NumericUpDown
            {
                Name = "numImageJPEGQuality",
                Location = new Point(335, 100),  // 精确对齐
                Size = new Size(100, 38),
                Font = new Font("Microsoft YaHei UI", 10F),
                Minimum = 1,
                Maximum = 100,
                Value = 90,
                TextAlign = HorizontalAlignment.Center
            };

            var lblPercent = new Label
            {
                Text = "%",
                Location = new Point(445, 98),  // 与标签对齐
                Size = new Size(30, 42),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            grpQuality.Controls.AddRange(new Control[] {
                lblWidth, numWidth, lblHeight, numHeight, chkAspectRatio,
                lblDPI, numDPI, lblJPEGQuality, numJPEGQuality, lblPercent
            });

            parent.Controls.Add(grpQuality);
            yPos += 215;  // 增大间距
        }

        /// <summary>
        /// 创建图片输出选项 - 宽松布局设计
        /// </summary>
        private void CreateImageOutputOptions(Panel parent, ref int yPos)
        {
            var grpOutput = CreateGroupBox("输出选项", 25, yPos, 1120, 240);  // 增大宽度和高度

            // 第一行：转换所有幻灯片
            var chkAllSlides = CreateCheckBox("转换所有幻灯片", 35, 45, 350);
            chkAllSlides.Name = "chkImageAllSlides";
            chkAllSlides.Checked = true;

            // 第二行：指定幻灯片范围
            var chkSlideRange = CreateCheckBox("指定幻灯片范围", 35, 90, 350);  // 增大行距到45
            chkSlideRange.Name = "chkImageSlideRange";

            // 第三行：起始和结束幻灯片 - 水平对齐
            var lblStartSlide = new Label
            {
                Text = "起始幻灯片:",
                Location = new Point(55, 138),  // 与复选框对齐，增大行距
                Size = new Size(100, 42),  // 增大高度确保垂直居中
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            var numStartSlide = new NumericUpDown
            {
                Name = "numImageStartSlide",
                Location = new Point(160, 140),  // 精确对齐
                Size = new Size(100, 38),  // 增大尺寸
                Font = new Font("Microsoft YaHei UI", 10F),
                Minimum = 1,
                Maximum = 9999,
                Value = 1,
                TextAlign = HorizontalAlignment.Center,
                Enabled = false
            };

            var lblEndSlide = new Label
            {
                Text = "结束幻灯片:",
                Location = new Point(280, 138),  // 与起始幻灯片标签对齐
                Size = new Size(100, 42),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            var numEndSlide = new NumericUpDown
            {
                Name = "numImageEndSlide",
                Location = new Point(385, 140),  // 精确对齐
                Size = new Size(100, 38),
                Font = new Font("Microsoft YaHei UI", 10F),
                Minimum = 1,
                Maximum = 9999,
                Value = 1,
                TextAlign = HorizontalAlignment.Center,
                Enabled = false
            };

            // 命名模式 - 同一行
            var lblNaming = new Label
            {
                Text = "命名模式:",
                Location = new Point(505, 138),  // 与其他标签对齐
                Size = new Size(100, 42),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            var txtNaming = new TextBox
            {
                Name = "txtImageNaming",
                Location = new Point(610, 140),  // 精确对齐
                Size = new Size(180, 38),  // 增大尺寸
                Font = new Font("Microsoft YaHei UI", 10F),
                Text = "幻灯片{0}",
                TextAlign = HorizontalAlignment.Center
            };

            // 事件处理 - 增强禁用状态视觉效果
            chkSlideRange.CheckedChanged += (s, e) =>
            {
                numStartSlide.Enabled = chkSlideRange.Checked;
                numEndSlide.Enabled = chkSlideRange.Checked;
                numStartSlide.BackColor = chkSlideRange.Checked ? Color.White : Color.FromArgb(248, 248, 248);
                numEndSlide.BackColor = chkSlideRange.Checked ? Color.White : Color.FromArgb(248, 248, 248);
            };

            grpOutput.Controls.AddRange(new Control[] {
                chkAllSlides, chkSlideRange, lblStartSlide, numStartSlide,
                lblEndSlide, numEndSlide, lblNaming, txtNaming
            });

            parent.Controls.Add(grpOutput);
            yPos += 205;  // 增大间距
        }

        #endregion

        #region Web转换设置创建方法

        /// <summary>
        /// 创建HTML转换设置 - 宽松布局设计
        /// </summary>
        private void CreateHTMLConversionSettings(Panel parent, ref int yPos)
        {
            var grpHTML = CreateGroupBox("HTML转换设置", 25, yPos, 1120, 200);  // 增大宽度和高度

            // 第一行：启用HTML转换
            var chkHTMLConversion = CreateCheckBox("启用HTML转换", 35, 45, 350);
            chkHTMLConversion.Name = "chkWebHTML";

            // 第二行：嵌入选项 - 水平排列
            var chkEmbedImages = CreateCheckBox("嵌入图片", 35, 90, 280);  // 增大行距到45
            chkEmbedImages.Name = "chkHTMLEmbedImages";
            chkEmbedImages.Checked = true;

            var chkEmbedCSS = CreateCheckBox("嵌入CSS", 330, 90, 280);  // 调整位置
            chkEmbedCSS.Name = "chkHTMLEmbedCSS";
            chkEmbedCSS.Checked = true;

            var chkEmbedJS = CreateCheckBox("嵌入JavaScript", 625, 90, 300);  // 调整位置和宽度
            chkEmbedJS.Name = "chkHTMLEmbedJS";
            chkEmbedJS.Checked = true;

            // 第三行：HTML版本和响应式设计 - 水平对齐
            var lblHTMLVersion = new Label
            {
                Text = "HTML版本:",
                Location = new Point(35, 138),  // 与第一行对齐，增大行距
                Size = new Size(100, 42),  // 增大高度确保垂直居中
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            var cmbHTMLVersion = new ComboBox
            {
                Name = "cmbHTMLVersion",
                Location = new Point(140, 140),  // 精确对齐
                Size = new Size(140, 38),  // 增大尺寸
                Font = new Font("Microsoft YaHei UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbHTMLVersion.Items.AddRange(new string[] { "HTML4", "HTML5", "XHTML" });
            cmbHTMLVersion.SelectedIndex = 1; // 默认HTML5
            // 设置下拉框文字居中
            SetComboBoxTextCenter(cmbHTMLVersion);

            var chkResponsive = CreateCheckBox("响应式设计", 330, 140, 320);  // 与下拉框同一行
            chkResponsive.Name = "chkHTMLResponsive";
            chkResponsive.Checked = true;

            grpHTML.Controls.AddRange(new Control[] {
                chkHTMLConversion, chkEmbedImages, chkEmbedCSS, chkEmbedJS,
                lblHTMLVersion, cmbHTMLVersion, chkResponsive
            });

            parent.Controls.Add(grpHTML);
            yPos += 225;  // 增大间距
        }

        /// <summary>
        /// 创建XAML转换设置 - 宽松布局设计
        /// </summary>
        private void CreateXAMLConversionSettings(Panel parent, ref int yPos)
        {
            var grpXAML = CreateGroupBox("XAML转换设置", 25, yPos, 1120, 140);  // 增大宽度和高度

            // 第一行：启用XAML转换
            var chkXAMLConversion = CreateCheckBox("启用XAML转换", 35, 45, 350);
            chkXAMLConversion.Name = "chkWebXAML";

            // 第二行：包含动画和优化Silverlight - 水平排列
            var chkAnimations = CreateCheckBox("包含动画", 35, 90, 280);  // 增大行距到45
            chkAnimations.Name = "chkXAMLAnimations";

            var chkSilverlight = CreateCheckBox("优化Silverlight", 330, 90, 300);  // 调整位置和宽度
            chkSilverlight.Name = "chkXAMLSilverlight";

            grpXAML.Controls.AddRange(new Control[] {
                chkXAMLConversion, chkAnimations, chkSilverlight
            });

            parent.Controls.Add(grpXAML);
            yPos += 165;  // 增大间距
        }

        #endregion

        #region 其他格式转换设置创建方法





        /// <summary>
        /// 创建缩略图设置 - 宽松布局设计
        /// </summary>
        private void CreateThumbnailSettings(Panel parent, ref int yPos)
        {
            var grpThumbnail = CreateGroupBox("缩略图生成设置", 25, yPos, 1120, 200);  // 增大宽度和高度

            // 第一行：启用缩略图生成
            var chkThumbnail = CreateCheckBox("启用缩略图生成", 35, 45, 350);
            chkThumbnail.Name = "chkOtherThumbnail";

            // 第二行：缩略图尺寸和格式设置 - 水平对齐
            var lblThumbWidth = new Label
            {
                Text = "缩略图宽度:",
                Location = new Point(35, 93),  // 与第一行对齐，增大行距
                Size = new Size(110, 42),  // 增大高度确保垂直居中
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            var numThumbWidth = new NumericUpDown
            {
                Name = "numThumbnailWidth",
                Location = new Point(150, 95),  // 精确对齐
                Size = new Size(100, 38),  // 增大尺寸
                Font = new Font("Microsoft YaHei UI", 10F),
                Minimum = 50,
                Maximum = 1000,
                Value = 200,
                TextAlign = HorizontalAlignment.Center
            };

            var lblThumbHeight = new Label
            {
                Text = "缩略图高度:",
                Location = new Point(270, 93),  // 与宽度标签对齐
                Size = new Size(110, 42),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            var numThumbHeight = new NumericUpDown
            {
                Name = "numThumbnailHeight",
                Location = new Point(385, 95),  // 精确对齐
                Size = new Size(100, 38),
                Font = new Font("Microsoft YaHei UI", 10F),
                Minimum = 50,
                Maximum = 1000,
                Value = 150,
                TextAlign = HorizontalAlignment.Center
            };

            var lblThumbFormat = new Label
            {
                Text = "格式:",
                Location = new Point(505, 93),  // 与其他标签对齐
                Size = new Size(70, 42),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(50, 50, 50)
            };

            var cmbThumbFormat = new ComboBox
            {
                Name = "cmbThumbnailFormat",
                Location = new Point(580, 95),  // 精确对齐
                Size = new Size(100, 38),  // 增大尺寸
                Font = new Font("Microsoft YaHei UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbThumbFormat.Items.AddRange(new string[] { "PNG", "JPEG", "BMP" });
            cmbThumbFormat.SelectedIndex = 0; // 默认PNG
            // 设置下拉框文字居中
            SetComboBoxTextCenter(cmbThumbFormat);

            // 第三行：为所有幻灯片生成
            var chkAllSlides = CreateCheckBox("为所有幻灯片生成", 35, 140, 350);  // 增大行距
            chkAllSlides.Name = "chkThumbnailAllSlides";
            chkAllSlides.Checked = true;

            grpThumbnail.Controls.AddRange(new Control[] {
                chkThumbnail, lblThumbWidth, numThumbWidth, lblThumbHeight, numThumbHeight,
                lblThumbFormat, cmbThumbFormat, chkAllSlides
            });

            parent.Controls.Add(grpThumbnail);
            yPos += 205;  // 增大间距
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 验证设置有效性
                if (!ValidateSettings())
                {
                    return;
                }

                SaveUIToSettings();
                var config = ConfigService.Instance.GetConfig();
                config.PPTFormatConversionSettings = _currentSettings;
                ConfigService.Instance.UpdateConfig(config);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 验证设置有效性
        /// </summary>
        private bool ValidateSettings()
        {
            // 验证PDF设置
            var pdfPanel = _tabPanels["PDF"];
            var pdfMasterSwitch = FindControlByName(pdfPanel, "chkPDFMaster") as CheckBox;
            if (pdfMasterSwitch?.Checked == true)
            {
                var startPage = GetControlValue<int>(pdfPanel, "numPDFStartPage");
                var endPage = GetControlValue<int>(pdfPanel, "numPDFEndPage");
                var usePageRange = GetControlValue<bool>(pdfPanel, "chkPDFPageRange");

                if (usePageRange && startPage > endPage)
                {
                    MessageBox.Show("PDF转换设置中，起始页不能大于结束页", "设置错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }

            // 验证图片设置
            var imagePanel = _tabPanels["Image"];
            var imageMasterSwitch = FindControlByName(imagePanel, "chkImageMaster") as CheckBox;
            if (imageMasterSwitch?.Checked == true)
            {
                var startSlide = GetControlValue<int>(imagePanel, "numImageStartSlide");
                var endSlide = GetControlValue<int>(imagePanel, "numImageEndSlide");
                var useSlideRange = GetControlValue<bool>(imagePanel, "chkImageSlideRange");

                if (useSlideRange && startSlide > endSlide)
                {
                    MessageBox.Show("图片转换设置中，起始幻灯片不能大于结束幻灯片", "设置错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                // 检查是否至少选择了一种图片格式
                bool anyFormatSelected = GetControlValue<bool>(imagePanel, "chkImagePNG") ||
                                       GetControlValue<bool>(imagePanel, "chkImageJPEG") ||
                                       GetControlValue<bool>(imagePanel, "chkImageBMP") ||
                                       GetControlValue<bool>(imagePanel, "chkImageTIFF") ||
                                       GetControlValue<bool>(imagePanel, "chkImageSVG");

                if (!anyFormatSelected)
                {
                    MessageBox.Show("启用图片转换时，必须至少选择一种图片格式", "设置错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            // 删除多余的确认提示框，直接关闭窗体
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 应用按钮点击事件
        /// </summary>
        private void BtnApply_Click(object? sender, EventArgs e)
        {
            try
            {
                SaveUIToSettings();
                var config = ConfigService.Instance.GetConfig();
                config.PPTFormatConversionSettings = _currentSettings;
                ConfigService.Instance.UpdateConfig(config);

                MessageBox.Show("设置已应用", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show("确定要重置所有设置为默认值吗？", "确认",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                _currentSettings = new PPTFormatConversionSettings();
                LoadSettingsToUI();
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void PPTFormatConversionForm_FormClosing(object? sender, FormClosingEventArgs e)
        {
            // 删除多余的确认提示框，简化关闭逻辑
            // 如果用户点击了确定按钮，设置已经保存，无需额外处理
        }

        /// <summary>
        /// 窗体大小改变事件
        /// </summary>
        private void PPTFormatConversionForm_Resize(object? sender, EventArgs e)
        {
            SetupTabWidths();
        }

        #endregion

        #region 数据加载和保存方法

        /// <summary>
        /// 加载设置到界面
        /// </summary>
        private void LoadSettingsToUI()
        {
            try
            {
                LoadPDFSettings();
                LoadImageSettings();
                LoadWebSettings();
                LoadOtherSettings();

                // 设置主开关状态
                SetMasterSwitchStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载设置到界面时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置主开关状态
        /// </summary>
        private void SetMasterSwitchStates()
        {
            // PDF主开关状态
            var pdfPanel = _tabPanels["PDF"];
            var pdfMasterSwitch = FindControlByName(pdfPanel, "chkPDFMaster") as CheckBox;
            if (pdfMasterSwitch != null)
            {
                SetPDFControlsEnabled(pdfPanel, pdfMasterSwitch.Checked);
            }

            // 图片主开关状态
            var imagePanel = _tabPanels["Image"];
            var imageMasterSwitch = FindControlByName(imagePanel, "chkImageMaster") as CheckBox;
            if (imageMasterSwitch != null)
            {
                SetImageControlsEnabled(imagePanel, imageMasterSwitch.Checked);
            }

            // Web主开关状态
            var webPanel = _tabPanels["Web"];
            var webMasterSwitch = FindControlByName(webPanel, "chkWebMaster") as CheckBox;
            if (webMasterSwitch != null)
            {
                SetWebControlsEnabled(webPanel, webMasterSwitch.Checked);
            }

            // 其他格式主开关状态
            var otherPanel = _tabPanels["Other"];
            var otherMasterSwitch = FindControlByName(otherPanel, "chkOtherMaster") as CheckBox;
            if (otherMasterSwitch != null)
            {
                SetOtherControlsEnabled(otherPanel, otherMasterSwitch.Checked);
            }
        }

        /// <summary>
        /// 保存界面设置
        /// </summary>
        private void SaveUIToSettings()
        {
            try
            {
                SavePDFSettings();
                SaveImageSettings();
                SaveWebSettings();
                SaveOtherSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存界面设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 加载PDF设置
        /// </summary>
        private void LoadPDFSettings()
        {
            var panel = _tabPanels["PDF"];
            var settings = _currentSettings.PDFSettings;

            SetControlValue(panel, "chkPDFMaster", settings.EnablePDFConversion);
            SetControlValue(panel, "chkPDFBatchConversion", settings.BatchConversion);
            SetControlValue(panel, "chkPDFKeepFormat", settings.KeepOriginalFormat);
            SetControlValue(panel, "chkPDFPageRange", settings.UsePageRange);
            SetControlValue(panel, "numPDFStartPage", settings.StartPage);
            SetControlValue(panel, "numPDFEndPage", settings.EndPage);
            SetControlValue(panel, "numPDFImageQuality", settings.ImageQuality);
            SetControlValue(panel, "cmbPDFCompression", settings.CompressionLevel);
            SetControlValue(panel, "chkPDFPassword", settings.PasswordProtection);
            SetControlValue(panel, "txtPDFPassword", settings.Password);
            SetControlValue(panel, "cmbPDFLayout", settings.SlideLayout);
            SetControlValue(panel, "chkPDFHandout", settings.HandoutMultipleSlides);
            SetControlValue(panel, "cmbPDFSlidesPerPage", settings.HandoutSlidesPerPage.ToString());
            SetControlValue(panel, "chkPDFNotes", settings.IncludeNotes);
            SetControlValue(panel, "cmbPDFNotesPosition", settings.NotesPosition);
            SetControlValue(panel, "chkPDFEmbedFonts", settings.EmbedFonts);
            SetControlValue(panel, "chkPDFCompressImages", settings.CompressImages);
            SetControlValue(panel, "chkPDFPreserveMetadata", settings.PreserveMetadata);
            SetControlValue(panel, "chkPDFBookmarks", settings.GenerateBookmarks);
            SetControlValue(panel, "cmbPDFCompliance", settings.PDFCompliance);
        }

        /// <summary>
        /// 保存PDF设置
        /// </summary>
        private void SavePDFSettings()
        {
            var panel = _tabPanels["PDF"];
            var settings = _currentSettings.PDFSettings;

            settings.EnablePDFConversion = GetControlValue<bool>(panel, "chkPDFMaster");
            settings.BatchConversion = GetControlValue<bool>(panel, "chkPDFBatchConversion");
            settings.KeepOriginalFormat = GetControlValue<bool>(panel, "chkPDFKeepFormat");
            settings.UsePageRange = GetControlValue<bool>(panel, "chkPDFPageRange");
            settings.StartPage = GetControlValue<int>(panel, "numPDFStartPage");
            settings.EndPage = GetControlValue<int>(panel, "numPDFEndPage");
            settings.ImageQuality = GetControlValue<int>(panel, "numPDFImageQuality");
            settings.CompressionLevel = GetControlValue<string>(panel, "cmbPDFCompression") ?? "标准";
            settings.PasswordProtection = GetControlValue<bool>(panel, "chkPDFPassword");
            settings.Password = GetControlValue<string>(panel, "txtPDFPassword") ?? "";
            settings.SlideLayout = GetControlValue<string>(panel, "cmbPDFLayout") ?? "幻灯片布局";
            settings.HandoutMultipleSlides = GetControlValue<bool>(panel, "chkPDFHandout");
            if (int.TryParse(GetControlValue<string>(panel, "cmbPDFSlidesPerPage"), out int slidesPerPage))
                settings.HandoutSlidesPerPage = slidesPerPage;
            settings.IncludeNotes = GetControlValue<bool>(panel, "chkPDFNotes");
            settings.NotesPosition = GetControlValue<string>(panel, "cmbPDFNotesPosition") ?? "底部";
            settings.EmbedFonts = GetControlValue<bool>(panel, "chkPDFEmbedFonts");
            settings.CompressImages = GetControlValue<bool>(panel, "chkPDFCompressImages");
            settings.PreserveMetadata = GetControlValue<bool>(panel, "chkPDFPreserveMetadata");
            settings.GenerateBookmarks = GetControlValue<bool>(panel, "chkPDFBookmarks");
            settings.PDFCompliance = GetControlValue<string>(panel, "cmbPDFCompliance") ?? "PDF 1.5";
        }

        #endregion

        #region 主开关控制方法

        /// <summary>
        /// 设置PDF控件启用状态
        /// </summary>
        private void SetPDFControlsEnabled(Panel panel, bool enabled)
        {
            foreach (Control control in panel.Controls)
            {
                if (control is GroupBox groupBox)
                {
                    foreach (Control subControl in groupBox.Controls)
                    {
                        if (subControl.Name != "chkPDFMaster")
                        {
                            subControl.Enabled = enabled;
                        }
                    }
                }
                else if (control.Name != "chkPDFMaster")
                {
                    control.Enabled = enabled;
                }
            }
        }

        /// <summary>
        /// 设置图片控件启用状态
        /// </summary>
        private void SetImageControlsEnabled(Panel panel, bool enabled)
        {
            foreach (Control control in panel.Controls)
            {
                if (control is GroupBox groupBox)
                {
                    foreach (Control subControl in groupBox.Controls)
                    {
                        if (subControl.Name != "chkImageMaster")
                        {
                            subControl.Enabled = enabled;
                        }
                    }
                }
                else if (control.Name != "chkImageMaster")
                {
                    control.Enabled = enabled;
                }
            }
        }

        /// <summary>
        /// 设置Web控件启用状态
        /// </summary>
        private void SetWebControlsEnabled(Panel panel, bool enabled)
        {
            foreach (Control control in panel.Controls)
            {
                if (control is GroupBox groupBox)
                {
                    foreach (Control subControl in groupBox.Controls)
                    {
                        if (subControl.Name != "chkWebMaster")
                        {
                            subControl.Enabled = enabled;
                        }
                    }
                }
                else if (control.Name != "chkWebMaster")
                {
                    control.Enabled = enabled;
                }
            }
        }

        /// <summary>
        /// 设置其他格式控件启用状态
        /// </summary>
        private void SetOtherControlsEnabled(Panel panel, bool enabled)
        {
            foreach (Control control in panel.Controls)
            {
                if (control is GroupBox groupBox)
                {
                    foreach (Control subControl in groupBox.Controls)
                    {
                        if (subControl.Name != "chkOtherMaster")
                        {
                            subControl.Enabled = enabled;
                        }
                    }
                }
                else if (control.Name != "chkOtherMaster")
                {
                    control.Enabled = enabled;
                }
            }
        }

        #endregion
    }
}

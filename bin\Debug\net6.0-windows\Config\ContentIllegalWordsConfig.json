{
  // 内容违禁词配置文件 - 控制PPT内容中违禁词的检测和处理
  // 此文件定义了违禁词过滤规则、搜索范围和处理方式等参数

  // 启用内容违禁词过滤 - 是否启用PPT内容违禁词检测功能
  "EnableContentIllegalWordsFilter": false,

  // 区分大小写 - 违禁词匹配是否区分大小写
  "CaseSensitive": false,

  // 使用正则表达式 - 是否将违禁词作为正则表达式处理
  "UseRegex": false,

  // 匹配完整单词 - 是否只匹配完整的单词（避免部分匹配）
  "MatchWholeWords": true,

  // 搜索标题 - 是否在幻灯片标题中搜索违禁词
  "SearchInTitles": true,

  // 搜索内容 - 是否在幻灯片正文内容中搜索违禁词
  "SearchInContent": true,

  // 搜索备注 - 是否在幻灯片备注中搜索违禁词
  "SearchInNotes": false,

  // 搜索注释 - 是否在幻灯片注释中搜索违禁词
  "SearchInComments": false,

  // 违禁词列表 - 需要检测的违禁词汇，支持Unicode字符、特殊符号和正则表达式
  "IllegalWords": [
    "示例敏感词1",
    "示例敏感词2",
    "包含\"引号\"的敏感词",
    "包含\\反斜杠的敏感词",
    "包含\n换行符的敏感词",
    "包含\t制表符的敏感词",
    "包含特殊字符的词：!@#$%^&*()",
    "正则表达式示例：\\d{4}-\\d{2}-\\d{2}",
    "Unicode字符：你好世界",
    "Emoji表情：😀😃😄"
  ],

  // 替换动作 - 发现违禁词时的处理方式：0=删除整个文本框，1=替换违禁词，2=删除包含违禁词的段落
  "ReplacementAction": 1,

  // 替换文本 - 当替换动作为1时，用于替换违禁词的文本
  "ReplacementText": "***",

  // 高亮违禁词 - 是否在处理过程中高亮显示发现的违禁词
  "HighlightIllegalWords": true,

  // 记录违禁词 - 是否在日志中记录发现的违禁词
  "LogIllegalWords": true,

  // 配置说明 - 此配置文件的功能描述和参数说明
  "Description": "内容非法词过滤配置。支持所有Unicode字符和特殊符号。ReplacementAction: 0=删除整个文本框, 1=替换非法词, 2=删除包含非法词的段落"
}

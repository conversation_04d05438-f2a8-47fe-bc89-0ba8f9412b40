namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 支持格式选择窗体的部分类定义
    /// 用于让用户选择要处理的PowerPoint文件格式
    /// </summary>
    partial class SupportedFormatsForm
    {
        /// <summary>
        /// 设计器所需的变量
        /// 用于管理窗体中的所有组件
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源
        /// 释放窗体占用的内存和系统资源
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false</param>
        protected override void Dispose(bool disposing)
        {
            // 如果需要释放托管资源且组件容器不为空
            if (disposing && (components != null))
            {
                // 释放组件容器中的所有组件
                components.Dispose();
            }
            // 调用基类的释放方法
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// 设计器支持所需的方法 - 不要使用代码编辑器修改此方法的内容
        /// 此方法由Windows Forms设计器自动生成，包含所有控件的初始化代码
        /// </summary>
        private void InitializeComponent()
        {
            // 创建资源管理器，用于加载窗体图标等资源
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(SupportedFormatsForm));

            // 初始化所有控件实例
            panelFormats = new Panel();        // 格式选择面板
            btnOK = new Button();              // 确定按钮
            btnCancel = new Button();          // 取消按钮
            btnSelectAll = new Button();       // 全选按钮
            btnDeselectAll = new Button();     // 取消全选按钮
            lblTitle = new Label();            // 标题标签
            lblDescription = new Label();      // 描述标签

            // 暂停布局逻辑，提高性能
            SuspendLayout();
            //
            // panelFormats - 格式选择面板配置
            //
            // 设置面板的锚定方式：上下左右都锚定，随窗体大小变化而调整
            panelFormats.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            panelFormats.AutoScroll = true;                          // 启用自动滚动条
            panelFormats.BackColor = Color.White;                    // 设置背景色为白色
            panelFormats.BorderStyle = BorderStyle.FixedSingle;      // 设置单线边框样式
            panelFormats.Location = new Point(12, 85);               // 设置面板位置
            panelFormats.Name = "panelFormats";                      // 设置控件名称
            panelFormats.Size = new Size(600, 700);                  // 设置面板大小
            panelFormats.TabIndex = 0;                               // 设置Tab键顺序

            //
            // btnOK - 确定按钮配置
            //
            // 设置按钮锚定在窗体底部右侧
            btnOK.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnOK.BackColor = Color.FromArgb(0, 123, 255);           // 设置蓝色背景（Bootstrap主色调）
            btnOK.FlatAppearance.BorderSize = 0;                     // 移除边框
            btnOK.FlatStyle = FlatStyle.Flat;                        // 设置扁平化样式
            btnOK.ForeColor = Color.White;                           // 设置白色文字
            btnOK.Location = new Point(315, 420);                    // 设置按钮位置
            btnOK.Name = "btnOK";                                    // 设置控件名称
            btnOK.Size = new Size(80, 35);                           // 设置按钮大小
            btnOK.TabIndex = 1;                                      // 设置Tab键顺序
            btnOK.Text = "确定";                                     // 设置按钮文本
            btnOK.UseVisualStyleBackColor = false;                   // 禁用视觉样式背景色
            btnOK.Click += BtnOK_Click;                              // 绑定点击事件处理器

            //
            // btnCancel - 取消按钮配置
            //
            // 设置按钮锚定在窗体底部右侧
            btnCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnCancel.BackColor = Color.FromArgb(108, 117, 125);     // 设置灰色背景（Bootstrap次要色调）
            btnCancel.FlatAppearance.BorderSize = 0;                 // 移除边框
            btnCancel.FlatStyle = FlatStyle.Flat;                    // 设置扁平化样式
            btnCancel.ForeColor = Color.White;                       // 设置白色文字
            btnCancel.Location = new Point(405, 420);                // 设置按钮位置
            btnCancel.Name = "btnCancel";                            // 设置控件名称
            btnCancel.Size = new Size(80, 35);                       // 设置按钮大小
            btnCancel.TabIndex = 2;                                  // 设置Tab键顺序
            btnCancel.Text = "取消";                                 // 设置按钮文本
            btnCancel.UseVisualStyleBackColor = false;               // 禁用视觉样式背景色
            btnCancel.Click += BtnCancel_Click;                      // 绑定点击事件处理器
            //
            // btnSelectAll - 全选按钮配置
            //
            // 设置按钮锚定在窗体底部左侧
            btnSelectAll.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            btnSelectAll.BackColor = Color.FromArgb(40, 167, 69);        // 设置绿色背景（Bootstrap成功色调）
            btnSelectAll.FlatAppearance.BorderSize = 0;                  // 移除边框
            btnSelectAll.FlatStyle = FlatStyle.Flat;                     // 设置扁平化样式
            btnSelectAll.ForeColor = Color.White;                        // 设置白色文字
            btnSelectAll.Location = new Point(12, 420);                  // 设置按钮位置
            btnSelectAll.Name = "btnSelectAll";                          // 设置控件名称
            btnSelectAll.Size = new Size(80, 35);                        // 设置按钮大小
            btnSelectAll.TabIndex = 3;                                   // 设置Tab键顺序
            btnSelectAll.Text = "全选";                                  // 设置按钮文本
            btnSelectAll.UseVisualStyleBackColor = false;                // 禁用视觉样式背景色
            btnSelectAll.Click += BtnSelectAll_Click;                    // 绑定点击事件处理器

            //
            // btnDeselectAll - 取消全选按钮配置
            //
            // 设置按钮锚定在窗体底部左侧
            btnDeselectAll.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            btnDeselectAll.BackColor = Color.FromArgb(220, 53, 69);      // 设置红色背景（Bootstrap危险色调）
            btnDeselectAll.FlatAppearance.BorderSize = 0;                // 移除边框
            btnDeselectAll.FlatStyle = FlatStyle.Flat;                   // 设置扁平化样式
            btnDeselectAll.ForeColor = Color.White;                      // 设置白色文字
            btnDeselectAll.Location = new Point(102, 420);               // 设置按钮位置
            btnDeselectAll.Name = "btnDeselectAll";                      // 设置控件名称
            btnDeselectAll.Size = new Size(80, 35);                      // 设置按钮大小
            btnDeselectAll.TabIndex = 4;                                 // 设置Tab键顺序
            btnDeselectAll.Text = "取消全选";                            // 设置按钮文本
            btnDeselectAll.UseVisualStyleBackColor = false;              // 禁用视觉样式背景色
            btnDeselectAll.Click += BtnDeselectAll_Click;                // 绑定点击事件处理器

            //
            // lblTitle - 标题标签配置
            //
            lblTitle.AutoSize = true;                                    // 启用自动调整大小
            // 设置字体：微软雅黑UI，12号，粗体
            lblTitle.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold, GraphicsUnit.Point);
            lblTitle.ForeColor = Color.FromArgb(33, 37, 41);             // 设置深灰色文字（Bootstrap主文字色）
            lblTitle.Location = new Point(12, 15);                       // 设置标签位置
            lblTitle.Name = "lblTitle";                                  // 设置控件名称
            lblTitle.Size = new Size(182, 31);                           // 设置标签大小
            lblTitle.TabIndex = 5;                                       // 设置Tab键顺序
            lblTitle.Text = "支持的文件格式";                            // 设置标签文本

            //
            // lblDescription - 描述标签配置
            //
            // 设置标签锚定在窗体顶部，左右拉伸
            lblDescription.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            lblDescription.ForeColor = Color.FromArgb(108, 117, 125);    // 设置浅灰色文字（Bootstrap次要文字色）
            lblDescription.Location = new Point(12, 45);                 // 设置标签位置
            lblDescription.Name = "lblDescription";                      // 设置控件名称
            lblDescription.Size = new Size(460, 30);                     // 设置标签大小
            lblDescription.TabIndex = 6;                                 // 设置Tab键顺序
            lblDescription.Text = "请选择要处理的PowerPoint文件格式";    // 设置标签文本
            //
            // SupportedFormatsForm - 支持格式选择窗体配置
            //
            AutoScaleDimensions = new SizeF(12F, 27F);                   // 设置自动缩放尺寸
            AutoScaleMode = AutoScaleMode.Font;                          // 设置按字体自动缩放模式
            BackColor = Color.FromArgb(248, 249, 250);                   // 设置窗体背景色为浅灰色
            ClientSize = new Size(500, 475);                             // 设置窗体客户区大小

            // 将所有控件添加到窗体中（按Z轴顺序，后添加的在上层）
            Controls.Add(lblDescription);                                // 添加描述标签
            Controls.Add(lblTitle);                                      // 添加标题标签
            Controls.Add(btnDeselectAll);                                // 添加取消全选按钮
            Controls.Add(btnSelectAll);                                  // 添加全选按钮
            Controls.Add(btnCancel);                                     // 添加取消按钮
            Controls.Add(btnOK);                                         // 添加确定按钮
            Controls.Add(panelFormats);                                  // 添加格式选择面板

            // 设置窗体字体：微软雅黑UI，10号，常规样式
            Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;               // 设置固定对话框边框样式（不可调整大小）
            Icon = (Icon)resources.GetObject("$this.Icon");              // 从资源中加载窗体图标
            Name = "SupportedFormatsForm";                               // 设置窗体名称
            StartPosition = FormStartPosition.CenterParent;              // 设置窗体在父窗体中央显示
            Text = "支持格式设置";                                       // 设置窗体标题栏文本

            // 恢复布局逻辑并执行布局
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        // 控件声明区域 - 所有窗体控件的私有字段声明
        private Panel panelFormats;        // 格式选择面板，用于显示可选择的文件格式复选框
        private Button btnOK;              // 确定按钮，确认选择并关闭窗体
        private Button btnCancel;          // 取消按钮，取消操作并关闭窗体
        private Button btnSelectAll;       // 全选按钮，选择所有文件格式
        private Button btnDeselectAll;     // 取消全选按钮，取消选择所有文件格式
        private Label lblTitle;            // 标题标签，显示窗体主标题
        private Label lblDescription;      // 描述标签，显示操作说明文本
    }
}

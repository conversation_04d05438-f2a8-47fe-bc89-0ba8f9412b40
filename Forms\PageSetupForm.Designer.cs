namespace PPTPiliangChuli.Forms
{
    partial class PageSetupForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(PageSetupForm));
            tabControl = new TabControl();
            tabPageSize = new TabPage();
            groupBoxSlideSize = new GroupBox();
            chkEnablePresetSizes = new CheckBox();
            lblPresetSizes = new Label();
            comboPresetSizes = new ComboBox();
            chkEnableSizeType = new CheckBox();
            radioStandard = new RadioButton();
            radioWidescreen = new RadioButton();
            radioCustom = new RadioButton();
            chkEnableCustomSize = new CheckBox();
            lblWidth = new Label();
            numWidth = new NumericUpDown();
            lblWidthUnit = new Label();
            comboWidthUnit = new ComboBox();
            lblHeight = new Label();
            numHeight = new NumericUpDown();
            lblHeightUnit = new Label();
            comboHeightUnit = new ComboBox();
            groupBoxAspectRatio = new GroupBox();
            chkEnableAspectRatio = new CheckBox();
            lblCurrentRatio = new Label();
            lblRatioValue = new Label();
            chkMaintainRatio = new CheckBox();
            tabPageOrientation = new TabPage();
            groupBoxOrientation = new GroupBox();
            chkEnableOrientation = new CheckBox();
            lblOrientationDescription = new Label();
            radioLandscape = new RadioButton();
            radioPortrait = new RadioButton();
            lblLandscapeDescription = new Label();
            lblPortraitDescription = new Label();
            tabPageBackground = new TabPage();
            groupBoxBackground = new GroupBox();
            chkEnableNoBackground = new CheckBox();
            chkEnableSolidColor = new CheckBox();
            chkEnableGradient = new CheckBox();
            chkEnableImageBackground = new CheckBox();
            radioNoBackground = new RadioButton();
            radioSolidColor = new RadioButton();
            radioGradient = new RadioButton();
            radioImage = new RadioButton();
            lblBackgroundColor = new Label();
            panelColorPicker = new Panel();
            btnSelectColor = new Button();
            lblGradientStart = new Label();
            panelGradientStart = new Panel();
            btnSelectGradientStart = new Button();
            lblGradientEnd = new Label();
            panelGradientEnd = new Panel();
            btnSelectGradientEnd = new Button();
            lblImagePath = new Label();
            btnSelectImage = new Button();
            comboImageFillMode = new ComboBox();
            lblImageFillMode = new Label();
            btnOK = new Button();
            btnCancel = new Button();
            btnApply = new Button();
            lblTitle = new Label();
            tabControl.SuspendLayout();
            tabPageSize.SuspendLayout();
            groupBoxSlideSize.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numWidth).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numHeight).BeginInit();
            groupBoxAspectRatio.SuspendLayout();
            tabPageOrientation.SuspendLayout();
            groupBoxOrientation.SuspendLayout();
            tabPageBackground.SuspendLayout();
            groupBoxBackground.SuspendLayout();
            SuspendLayout();
            // 
            // tabControl
            // 
            tabControl.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            tabControl.Controls.Add(tabPageSize);
            tabControl.Controls.Add(tabPageOrientation);
            tabControl.Controls.Add(tabPageBackground);
            tabControl.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            tabControl.ItemSize = new Size(283, 45);
            tabControl.Location = new Point(20, 60);
            tabControl.Name = "tabControl";
            tabControl.SelectedIndex = 0;
            tabControl.Size = new Size(860, 580);
            tabControl.SizeMode = TabSizeMode.Fixed;
            tabControl.TabIndex = 0;
            // 
            // tabPageSize
            // 
            tabPageSize.Controls.Add(groupBoxSlideSize);
            tabPageSize.Controls.Add(groupBoxAspectRatio);
            tabPageSize.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            tabPageSize.Location = new Point(4, 49);
            tabPageSize.Name = "tabPageSize";
            tabPageSize.Padding = new Padding(20);
            tabPageSize.Size = new Size(852, 527);
            tabPageSize.TabIndex = 0;
            tabPageSize.Text = "幻灯片尺寸设置";
            tabPageSize.UseVisualStyleBackColor = true;
            // 
            // groupBoxSlideSize
            // 
            groupBoxSlideSize.Controls.Add(chkEnablePresetSizes);
            groupBoxSlideSize.Controls.Add(lblPresetSizes);
            groupBoxSlideSize.Controls.Add(comboPresetSizes);
            groupBoxSlideSize.Controls.Add(chkEnableSizeType);
            groupBoxSlideSize.Controls.Add(radioStandard);
            groupBoxSlideSize.Controls.Add(radioWidescreen);
            groupBoxSlideSize.Controls.Add(radioCustom);
            groupBoxSlideSize.Controls.Add(chkEnableCustomSize);
            groupBoxSlideSize.Controls.Add(lblWidth);
            groupBoxSlideSize.Controls.Add(numWidth);
            groupBoxSlideSize.Controls.Add(lblWidthUnit);
            groupBoxSlideSize.Controls.Add(comboWidthUnit);
            groupBoxSlideSize.Controls.Add(lblHeight);
            groupBoxSlideSize.Controls.Add(numHeight);
            groupBoxSlideSize.Controls.Add(lblHeightUnit);
            groupBoxSlideSize.Controls.Add(comboHeightUnit);
            groupBoxSlideSize.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            groupBoxSlideSize.Location = new Point(20, 20);
            groupBoxSlideSize.Name = "groupBoxSlideSize";
            groupBoxSlideSize.Size = new Size(810, 280);
            groupBoxSlideSize.TabIndex = 0;
            groupBoxSlideSize.TabStop = false;
            groupBoxSlideSize.Text = "幻灯片尺寸";
            // 
            // chkEnablePresetSizes
            // 
            chkEnablePresetSizes.AutoSize = true;
            chkEnablePresetSizes.Checked = true;
            chkEnablePresetSizes.CheckState = CheckState.Checked;
            chkEnablePresetSizes.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            chkEnablePresetSizes.Location = new Point(30, 40);
            chkEnablePresetSizes.Name = "chkEnablePresetSizes";
            chkEnablePresetSizes.Size = new Size(112, 24);
            chkEnablePresetSizes.TabIndex = 0;
            chkEnablePresetSizes.Text = "启用预设尺寸";
            chkEnablePresetSizes.UseVisualStyleBackColor = true;
            // 
            // lblPresetSizes
            // 
            lblPresetSizes.AutoSize = true;
            lblPresetSizes.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            lblPresetSizes.Location = new Point(60, 80);
            lblPresetSizes.Name = "lblPresetSizes";
            lblPresetSizes.Size = new Size(79, 20);
            lblPresetSizes.TabIndex = 1;
            lblPresetSizes.Text = "预设尺寸：";
            // 
            // comboPresetSizes
            // 
            comboPresetSizes.DropDownStyle = ComboBoxStyle.DropDownList;
            comboPresetSizes.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            comboPresetSizes.FormattingEnabled = true;
            comboPresetSizes.Location = new Point(180, 77);
            comboPresetSizes.Name = "comboPresetSizes";
            comboPresetSizes.Size = new Size(400, 27);
            comboPresetSizes.TabIndex = 2;
            // 
            // chkEnableSizeType
            // 
            chkEnableSizeType.AutoSize = true;
            chkEnableSizeType.Checked = true;
            chkEnableSizeType.CheckState = CheckState.Checked;
            chkEnableSizeType.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            chkEnableSizeType.Location = new Point(30, 120);
            chkEnableSizeType.Name = "chkEnableSizeType";
            chkEnableSizeType.Size = new Size(112, 24);
            chkEnableSizeType.TabIndex = 3;
            chkEnableSizeType.Text = "启用尺寸类型";
            chkEnableSizeType.UseVisualStyleBackColor = true;
            // 
            // radioStandard
            // 
            radioStandard.AutoSize = true;
            radioStandard.Checked = true;
            radioStandard.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            radioStandard.Location = new Point(60, 160);
            radioStandard.Name = "radioStandard";
            radioStandard.Size = new Size(120, 24);
            radioStandard.TabIndex = 4;
            radioStandard.TabStop = true;
            radioStandard.Text = "标准 (4:3 比例)";
            radioStandard.UseVisualStyleBackColor = true;
            // 
            // radioWidescreen
            // 
            radioWidescreen.AutoSize = true;
            radioWidescreen.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            radioWidescreen.Location = new Point(250, 160);
            radioWidescreen.Name = "radioWidescreen";
            radioWidescreen.Size = new Size(128, 24);
            radioWidescreen.TabIndex = 5;
            radioWidescreen.Text = "宽屏 (16:9 比例)";
            radioWidescreen.UseVisualStyleBackColor = true;
            // 
            // radioCustom
            // 
            radioCustom.AutoSize = true;
            radioCustom.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            radioCustom.Location = new Point(450, 160);
            radioCustom.Name = "radioCustom";
            radioCustom.Size = new Size(69, 24);
            radioCustom.TabIndex = 6;
            radioCustom.Text = "自定义";
            radioCustom.UseVisualStyleBackColor = true;
            // 
            // chkEnableCustomSize
            // 
            chkEnableCustomSize.AutoSize = true;
            chkEnableCustomSize.Checked = true;
            chkEnableCustomSize.CheckState = CheckState.Checked;
            chkEnableCustomSize.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            chkEnableCustomSize.Location = new Point(30, 200);
            chkEnableCustomSize.Name = "chkEnableCustomSize";
            chkEnableCustomSize.Size = new Size(126, 24);
            chkEnableCustomSize.TabIndex = 7;
            chkEnableCustomSize.Text = "启用自定义尺寸";
            chkEnableCustomSize.UseVisualStyleBackColor = true;
            // 
            // lblWidth
            // 
            lblWidth.AutoSize = true;
            lblWidth.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            lblWidth.Location = new Point(60, 240);
            lblWidth.Name = "lblWidth";
            lblWidth.Size = new Size(51, 20);
            lblWidth.TabIndex = 8;
            lblWidth.Text = "宽度：";
            // 
            // numWidth
            // 
            numWidth.DecimalPlaces = 2;
            numWidth.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            numWidth.Location = new Point(140, 238);
            numWidth.Maximum = new decimal(new int[] { 1000, 0, 0, 0 });
            numWidth.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numWidth.Name = "numWidth";
            numWidth.Size = new Size(120, 24);
            numWidth.TabIndex = 9;
            numWidth.TextAlign = HorizontalAlignment.Center;
            numWidth.Value = new decimal(new int[] { 254, 0, 0, 0 });
            // 
            // lblWidthUnit
            // 
            lblWidthUnit.AutoSize = true;
            lblWidthUnit.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            lblWidthUnit.Location = new Point(60, 280);
            lblWidthUnit.Name = "lblWidthUnit";
            lblWidthUnit.Size = new Size(0, 20);
            lblWidthUnit.TabIndex = 14;
            lblWidthUnit.Visible = false;
            // 
            // comboWidthUnit
            // 
            comboWidthUnit.DropDownStyle = ComboBoxStyle.DropDownList;
            comboWidthUnit.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            comboWidthUnit.FormattingEnabled = true;
            comboWidthUnit.Location = new Point(280, 238);
            comboWidthUnit.Name = "comboWidthUnit";
            comboWidthUnit.Size = new Size(100, 27);
            comboWidthUnit.TabIndex = 10;
            // 
            // lblHeight
            // 
            lblHeight.AutoSize = true;
            lblHeight.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            lblHeight.Location = new Point(420, 240);
            lblHeight.Name = "lblHeight";
            lblHeight.Size = new Size(51, 20);
            lblHeight.TabIndex = 11;
            lblHeight.Text = "高度：";
            // 
            // numHeight
            // 
            numHeight.DecimalPlaces = 2;
            numHeight.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            numHeight.Location = new Point(500, 238);
            numHeight.Maximum = new decimal(new int[] { 1000, 0, 0, 0 });
            numHeight.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numHeight.Name = "numHeight";
            numHeight.Size = new Size(120, 24);
            numHeight.TabIndex = 12;
            numHeight.TextAlign = HorizontalAlignment.Center;
            numHeight.Value = new decimal(new int[] { 1905, 0, 0, 65536 });
            // 
            // lblHeightUnit
            // 
            lblHeightUnit.AutoSize = true;
            lblHeightUnit.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            lblHeightUnit.Location = new Point(420, 280);
            lblHeightUnit.Name = "lblHeightUnit";
            lblHeightUnit.Size = new Size(0, 20);
            lblHeightUnit.TabIndex = 15;
            lblHeightUnit.Visible = false;
            // 
            // comboHeightUnit
            // 
            comboHeightUnit.DropDownStyle = ComboBoxStyle.DropDownList;
            comboHeightUnit.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            comboHeightUnit.FormattingEnabled = true;
            comboHeightUnit.Location = new Point(640, 238);
            comboHeightUnit.Name = "comboHeightUnit";
            comboHeightUnit.Size = new Size(100, 27);
            comboHeightUnit.TabIndex = 13;
            // 
            // groupBoxAspectRatio
            // 
            groupBoxAspectRatio.Controls.Add(chkEnableAspectRatio);
            groupBoxAspectRatio.Controls.Add(lblCurrentRatio);
            groupBoxAspectRatio.Controls.Add(lblRatioValue);
            groupBoxAspectRatio.Controls.Add(chkMaintainRatio);
            groupBoxAspectRatio.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            groupBoxAspectRatio.Location = new Point(20, 320);
            groupBoxAspectRatio.Name = "groupBoxAspectRatio";
            groupBoxAspectRatio.Size = new Size(810, 180);
            groupBoxAspectRatio.TabIndex = 1;
            groupBoxAspectRatio.TabStop = false;
            groupBoxAspectRatio.Text = "尺寸比例调整";
            // 
            // chkEnableAspectRatio
            // 
            chkEnableAspectRatio.AutoSize = true;
            chkEnableAspectRatio.Checked = true;
            chkEnableAspectRatio.CheckState = CheckState.Checked;
            chkEnableAspectRatio.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            chkEnableAspectRatio.Location = new Point(30, 40);
            chkEnableAspectRatio.Name = "chkEnableAspectRatio";
            chkEnableAspectRatio.Size = new Size(112, 24);
            chkEnableAspectRatio.TabIndex = 0;
            chkEnableAspectRatio.Text = "启用比例调整";
            chkEnableAspectRatio.UseVisualStyleBackColor = true;
            // 
            // lblCurrentRatio
            // 
            lblCurrentRatio.AutoSize = true;
            lblCurrentRatio.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            lblCurrentRatio.Location = new Point(60, 80);
            lblCurrentRatio.Name = "lblCurrentRatio";
            lblCurrentRatio.Size = new Size(79, 20);
            lblCurrentRatio.TabIndex = 1;
            lblCurrentRatio.Text = "当前比例：";
            // 
            // lblRatioValue
            // 
            lblRatioValue.AutoSize = true;
            lblRatioValue.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Bold, GraphicsUnit.Point);
            lblRatioValue.ForeColor = Color.FromArgb(0, 123, 255);
            lblRatioValue.Location = new Point(180, 80);
            lblRatioValue.Name = "lblRatioValue";
            lblRatioValue.Size = new Size(31, 19);
            lblRatioValue.TabIndex = 2;
            lblRatioValue.Text = "4:3";
            // 
            // chkMaintainRatio
            // 
            chkMaintainRatio.AutoSize = true;
            chkMaintainRatio.Checked = true;
            chkMaintainRatio.CheckState = CheckState.Checked;
            chkMaintainRatio.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            chkMaintainRatio.Location = new Point(60, 120);
            chkMaintainRatio.Name = "chkMaintainRatio";
            chkMaintainRatio.Size = new Size(112, 24);
            chkMaintainRatio.TabIndex = 3;
            chkMaintainRatio.Text = "锁定宽高比例";
            chkMaintainRatio.UseVisualStyleBackColor = true;
            // 
            // tabPageOrientation
            // 
            tabPageOrientation.Controls.Add(groupBoxOrientation);
            tabPageOrientation.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            tabPageOrientation.Location = new Point(4, 49);
            tabPageOrientation.Name = "tabPageOrientation";
            tabPageOrientation.Padding = new Padding(20);
            tabPageOrientation.Size = new Size(852, 527);
            tabPageOrientation.TabIndex = 1;
            tabPageOrientation.Text = "幻灯片方向";
            tabPageOrientation.UseVisualStyleBackColor = true;
            // 
            // groupBoxOrientation
            // 
            groupBoxOrientation.Controls.Add(chkEnableOrientation);
            groupBoxOrientation.Controls.Add(lblOrientationDescription);
            groupBoxOrientation.Controls.Add(radioLandscape);
            groupBoxOrientation.Controls.Add(radioPortrait);
            groupBoxOrientation.Controls.Add(lblLandscapeDescription);
            groupBoxOrientation.Controls.Add(lblPortraitDescription);
            groupBoxOrientation.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            groupBoxOrientation.Location = new Point(20, 20);
            groupBoxOrientation.Name = "groupBoxOrientation";
            groupBoxOrientation.Size = new Size(810, 250);
            groupBoxOrientation.TabIndex = 0;
            groupBoxOrientation.TabStop = false;
            groupBoxOrientation.Text = "幻灯片方向设置";
            // 
            // chkEnableOrientation
            // 
            chkEnableOrientation.AutoSize = true;
            chkEnableOrientation.Checked = true;
            chkEnableOrientation.CheckState = CheckState.Checked;
            chkEnableOrientation.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            chkEnableOrientation.Location = new Point(50, 60);
            chkEnableOrientation.Name = "chkEnableOrientation";
            chkEnableOrientation.Size = new Size(112, 24);
            chkEnableOrientation.TabIndex = 0;
            chkEnableOrientation.Text = "启用方向设置";
            chkEnableOrientation.UseVisualStyleBackColor = true;
            // 
            // lblOrientationDescription
            // 
            lblOrientationDescription.AutoSize = true;
            lblOrientationDescription.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            lblOrientationDescription.ForeColor = Color.FromArgb(108, 117, 125);
            lblOrientationDescription.Location = new Point(50, 110);
            lblOrientationDescription.Name = "lblOrientationDescription";
            lblOrientationDescription.Size = new Size(373, 20);
            lblOrientationDescription.TabIndex = 3;
            lblOrientationDescription.Text = "选择幻灯片的显示方向，影响幻灯片的宽高比例和布局效果";
            // 
            // radioLandscape
            // 
            radioLandscape.AutoSize = true;
            radioLandscape.Checked = true;
            radioLandscape.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            radioLandscape.Location = new Point(50, 150);
            radioLandscape.Name = "radioLandscape";
            radioLandscape.Size = new Size(115, 24);
            radioLandscape.TabIndex = 1;
            radioLandscape.TabStop = true;
            radioLandscape.Text = "横向 (宽 > 高)";
            radioLandscape.UseVisualStyleBackColor = true;
            // 
            // radioPortrait
            // 
            radioPortrait.AutoSize = true;
            radioPortrait.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            radioPortrait.Location = new Point(350, 150);
            radioPortrait.Name = "radioPortrait";
            radioPortrait.Size = new Size(115, 24);
            radioPortrait.TabIndex = 2;
            radioPortrait.Text = "纵向 (高 > 宽)";
            radioPortrait.UseVisualStyleBackColor = true;
            // 
            // lblLandscapeDescription
            // 
            lblLandscapeDescription.AutoSize = true;
            lblLandscapeDescription.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            lblLandscapeDescription.ForeColor = Color.FromArgb(108, 117, 125);
            lblLandscapeDescription.Location = new Point(50, 200);
            lblLandscapeDescription.Name = "lblLandscapeDescription";
            lblLandscapeDescription.Size = new Size(164, 17);
            lblLandscapeDescription.TabIndex = 4;
            lblLandscapeDescription.Text = "适用于演示文稿、图表展示等";
            // 
            // lblPortraitDescription
            // 
            lblPortraitDescription.AutoSize = true;
            lblPortraitDescription.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            lblPortraitDescription.ForeColor = Color.FromArgb(108, 117, 125);
            lblPortraitDescription.Location = new Point(350, 200);
            lblPortraitDescription.Name = "lblPortraitDescription";
            lblPortraitDescription.Size = new Size(152, 17);
            lblPortraitDescription.TabIndex = 5;
            lblPortraitDescription.Text = "适用于文档、报告、海报等";
            // 
            // tabPageBackground
            // 
            tabPageBackground.Controls.Add(groupBoxBackground);
            tabPageBackground.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            tabPageBackground.Location = new Point(4, 49);
            tabPageBackground.Name = "tabPageBackground";
            tabPageBackground.Padding = new Padding(20);
            tabPageBackground.Size = new Size(852, 527);
            tabPageBackground.TabIndex = 2;
            tabPageBackground.Text = "背景设置";
            tabPageBackground.UseVisualStyleBackColor = true;
            // 
            // groupBoxBackground
            // 
            groupBoxBackground.Controls.Add(chkEnableNoBackground);
            groupBoxBackground.Controls.Add(chkEnableSolidColor);
            groupBoxBackground.Controls.Add(chkEnableGradient);
            groupBoxBackground.Controls.Add(chkEnableImageBackground);
            groupBoxBackground.Controls.Add(radioNoBackground);
            groupBoxBackground.Controls.Add(radioSolidColor);
            groupBoxBackground.Controls.Add(radioGradient);
            groupBoxBackground.Controls.Add(radioImage);
            groupBoxBackground.Controls.Add(lblBackgroundColor);
            groupBoxBackground.Controls.Add(panelColorPicker);
            groupBoxBackground.Controls.Add(btnSelectColor);
            groupBoxBackground.Controls.Add(lblGradientStart);
            groupBoxBackground.Controls.Add(panelGradientStart);
            groupBoxBackground.Controls.Add(btnSelectGradientStart);
            groupBoxBackground.Controls.Add(lblGradientEnd);
            groupBoxBackground.Controls.Add(panelGradientEnd);
            groupBoxBackground.Controls.Add(btnSelectGradientEnd);
            groupBoxBackground.Controls.Add(lblImagePath);
            groupBoxBackground.Controls.Add(btnSelectImage);
            groupBoxBackground.Controls.Add(comboImageFillMode);
            groupBoxBackground.Controls.Add(lblImageFillMode);
            groupBoxBackground.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            groupBoxBackground.Location = new Point(20, 20);
            groupBoxBackground.Name = "groupBoxBackground";
            groupBoxBackground.Size = new Size(810, 500);
            groupBoxBackground.TabIndex = 0;
            groupBoxBackground.TabStop = false;
            groupBoxBackground.Text = "背景设置";
            // 
            // chkEnableNoBackground
            // 
            chkEnableNoBackground.AutoSize = true;
            chkEnableNoBackground.Checked = true;
            chkEnableNoBackground.CheckState = CheckState.Checked;
            chkEnableNoBackground.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            chkEnableNoBackground.Location = new Point(30, 40);
            chkEnableNoBackground.Name = "chkEnableNoBackground";
            chkEnableNoBackground.Size = new Size(98, 24);
            chkEnableNoBackground.TabIndex = 0;
            chkEnableNoBackground.Text = "启用无背景";
            chkEnableNoBackground.UseVisualStyleBackColor = true;
            // 
            // chkEnableSolidColor
            // 
            chkEnableSolidColor.AutoSize = true;
            chkEnableSolidColor.Checked = true;
            chkEnableSolidColor.CheckState = CheckState.Checked;
            chkEnableSolidColor.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            chkEnableSolidColor.Location = new Point(30, 80);
            chkEnableSolidColor.Name = "chkEnableSolidColor";
            chkEnableSolidColor.Size = new Size(112, 24);
            chkEnableSolidColor.TabIndex = 1;
            chkEnableSolidColor.Text = "启用纯色背景";
            chkEnableSolidColor.UseVisualStyleBackColor = true;
            // 
            // chkEnableGradient
            // 
            chkEnableGradient.AutoSize = true;
            chkEnableGradient.Checked = true;
            chkEnableGradient.CheckState = CheckState.Checked;
            chkEnableGradient.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            chkEnableGradient.Location = new Point(30, 120);
            chkEnableGradient.Name = "chkEnableGradient";
            chkEnableGradient.Size = new Size(112, 24);
            chkEnableGradient.TabIndex = 2;
            chkEnableGradient.Text = "启用渐变背景";
            chkEnableGradient.UseVisualStyleBackColor = true;
            // 
            // chkEnableImageBackground
            // 
            chkEnableImageBackground.AutoSize = true;
            chkEnableImageBackground.Checked = true;
            chkEnableImageBackground.CheckState = CheckState.Checked;
            chkEnableImageBackground.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            chkEnableImageBackground.Location = new Point(30, 158);
            chkEnableImageBackground.Name = "chkEnableImageBackground";
            chkEnableImageBackground.Size = new Size(112, 24);
            chkEnableImageBackground.TabIndex = 3;
            chkEnableImageBackground.Text = "启用图片背景";
            chkEnableImageBackground.UseVisualStyleBackColor = true;
            // 
            // radioNoBackground
            // 
            radioNoBackground.AutoSize = true;
            radioNoBackground.Checked = true;
            radioNoBackground.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            radioNoBackground.Location = new Point(60, 200);
            radioNoBackground.Name = "radioNoBackground";
            radioNoBackground.Size = new Size(69, 24);
            radioNoBackground.TabIndex = 4;
            radioNoBackground.TabStop = true;
            radioNoBackground.Text = "无背景";
            radioNoBackground.UseVisualStyleBackColor = true;
            // 
            // radioSolidColor
            // 
            radioSolidColor.AutoSize = true;
            radioSolidColor.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            radioSolidColor.Location = new Point(200, 200);
            radioSolidColor.Name = "radioSolidColor";
            radioSolidColor.Size = new Size(55, 24);
            radioSolidColor.TabIndex = 5;
            radioSolidColor.Text = "纯色";
            radioSolidColor.UseVisualStyleBackColor = true;
            // 
            // radioGradient
            // 
            radioGradient.AutoSize = true;
            radioGradient.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            radioGradient.Location = new Point(300, 200);
            radioGradient.Name = "radioGradient";
            radioGradient.Size = new Size(55, 24);
            radioGradient.TabIndex = 6;
            radioGradient.Text = "渐变";
            radioGradient.UseVisualStyleBackColor = true;
            // 
            // radioImage
            // 
            radioImage.AutoSize = true;
            radioImage.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            radioImage.Location = new Point(400, 200);
            radioImage.Name = "radioImage";
            radioImage.Size = new Size(55, 24);
            radioImage.TabIndex = 7;
            radioImage.Text = "图片";
            radioImage.UseVisualStyleBackColor = true;
            // 
            // lblBackgroundColor
            // 
            lblBackgroundColor.AutoSize = true;
            lblBackgroundColor.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            lblBackgroundColor.Location = new Point(60, 240);
            lblBackgroundColor.Name = "lblBackgroundColor";
            lblBackgroundColor.Size = new Size(79, 20);
            lblBackgroundColor.TabIndex = 8;
            lblBackgroundColor.Text = "背景颜色：";
            // 
            // panelColorPicker
            // 
            panelColorPicker.BackColor = Color.White;
            panelColorPicker.BorderStyle = BorderStyle.FixedSingle;
            panelColorPicker.Location = new Point(180, 238);
            panelColorPicker.Name = "panelColorPicker";
            panelColorPicker.Size = new Size(80, 35);
            panelColorPicker.TabIndex = 9;
            panelColorPicker.Click += PanelColorPicker_Click;
            // 
            // btnSelectColor
            // 
            btnSelectColor.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            btnSelectColor.Location = new Point(280, 238);
            btnSelectColor.Name = "btnSelectColor";
            btnSelectColor.Size = new Size(120, 35);
            btnSelectColor.TabIndex = 10;
            btnSelectColor.Text = "选择颜色";
            btnSelectColor.UseVisualStyleBackColor = true;
            btnSelectColor.Click += BtnSelectColor_Click;
            // 
            // lblGradientStart
            // 
            lblGradientStart.AutoSize = true;
            lblGradientStart.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            lblGradientStart.Location = new Point(60, 290);
            lblGradientStart.Name = "lblGradientStart";
            lblGradientStart.Size = new Size(79, 20);
            lblGradientStart.TabIndex = 11;
            lblGradientStart.Text = "起始颜色：";
            // 
            // panelGradientStart
            // 
            panelGradientStart.BackColor = Color.White;
            panelGradientStart.BorderStyle = BorderStyle.FixedSingle;
            panelGradientStart.Location = new Point(180, 288);
            panelGradientStart.Name = "panelGradientStart";
            panelGradientStart.Size = new Size(80, 35);
            panelGradientStart.TabIndex = 12;
            panelGradientStart.Click += PanelGradientStart_Click;
            // 
            // btnSelectGradientStart
            // 
            btnSelectGradientStart.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            btnSelectGradientStart.Location = new Point(280, 288);
            btnSelectGradientStart.Name = "btnSelectGradientStart";
            btnSelectGradientStart.Size = new Size(150, 35);
            btnSelectGradientStart.TabIndex = 13;
            btnSelectGradientStart.Text = "选择起始颜色";
            btnSelectGradientStart.UseVisualStyleBackColor = true;
            btnSelectGradientStart.Click += BtnSelectGradientStart_Click;
            // 
            // lblGradientEnd
            // 
            lblGradientEnd.AutoSize = true;
            lblGradientEnd.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            lblGradientEnd.Location = new Point(60, 340);
            lblGradientEnd.Name = "lblGradientEnd";
            lblGradientEnd.Size = new Size(79, 20);
            lblGradientEnd.TabIndex = 14;
            lblGradientEnd.Text = "结束颜色：";
            // 
            // panelGradientEnd
            // 
            panelGradientEnd.BackColor = Color.Black;
            panelGradientEnd.BorderStyle = BorderStyle.FixedSingle;
            panelGradientEnd.Location = new Point(180, 338);
            panelGradientEnd.Name = "panelGradientEnd";
            panelGradientEnd.Size = new Size(80, 35);
            panelGradientEnd.TabIndex = 15;
            panelGradientEnd.Click += PanelGradientEnd_Click;
            // 
            // btnSelectGradientEnd
            // 
            btnSelectGradientEnd.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            btnSelectGradientEnd.Location = new Point(280, 338);
            btnSelectGradientEnd.Name = "btnSelectGradientEnd";
            btnSelectGradientEnd.Size = new Size(150, 35);
            btnSelectGradientEnd.TabIndex = 16;
            btnSelectGradientEnd.Text = "选择结束颜色";
            btnSelectGradientEnd.UseVisualStyleBackColor = true;
            btnSelectGradientEnd.Click += BtnSelectGradientEnd_Click;
            // 
            // lblImagePath
            // 
            lblImagePath.AutoSize = true;
            lblImagePath.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            lblImagePath.Location = new Point(60, 390);
            lblImagePath.Name = "lblImagePath";
            lblImagePath.Size = new Size(79, 20);
            lblImagePath.TabIndex = 17;
            lblImagePath.Text = "选择图片：";
            // 
            // btnSelectImage
            // 
            btnSelectImage.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            btnSelectImage.Location = new Point(180, 388);
            btnSelectImage.Name = "btnSelectImage";
            btnSelectImage.Size = new Size(150, 35);
            btnSelectImage.TabIndex = 18;
            btnSelectImage.Text = "选择图片";
            btnSelectImage.UseVisualStyleBackColor = true;
            btnSelectImage.Click += BtnSelectImage_Click;
            // 
            // comboImageFillMode
            // 
            comboImageFillMode.DropDownStyle = ComboBoxStyle.DropDownList;
            comboImageFillMode.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            comboImageFillMode.FormattingEnabled = true;
            comboImageFillMode.Location = new Point(180, 438);
            comboImageFillMode.Name = "comboImageFillMode";
            comboImageFillMode.Size = new Size(150, 27);
            comboImageFillMode.TabIndex = 20;
            comboImageFillMode.SelectedIndexChanged += ComboImageFillMode_SelectedIndexChanged;
            // 
            // lblImageFillMode
            // 
            lblImageFillMode.AutoSize = true;
            lblImageFillMode.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            lblImageFillMode.Location = new Point(60, 440);
            lblImageFillMode.Name = "lblImageFillMode";
            lblImageFillMode.Size = new Size(79, 20);
            lblImageFillMode.TabIndex = 19;
            lblImageFillMode.Text = "填充模式：";
            // 
            // btnOK
            // 
            btnOK.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnOK.BackColor = Color.FromArgb(0, 123, 255);
            btnOK.FlatAppearance.BorderSize = 0;
            btnOK.FlatStyle = FlatStyle.Flat;
            btnOK.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            btnOK.ForeColor = Color.White;
            btnOK.Location = new Point(580, 660);
            btnOK.Name = "btnOK";
            btnOK.Size = new Size(90, 40);
            btnOK.TabIndex = 1;
            btnOK.Text = "确定";
            btnOK.UseVisualStyleBackColor = false;
            btnOK.Click += BtnOK_Click;
            // 
            // btnCancel
            // 
            btnCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnCancel.BackColor = Color.FromArgb(108, 117, 125);
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            btnCancel.ForeColor = Color.White;
            btnCancel.Location = new Point(680, 660);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(90, 40);
            btnCancel.TabIndex = 2;
            btnCancel.Text = "取消";
            btnCancel.UseVisualStyleBackColor = false;
            btnCancel.Click += BtnCancel_Click;
            // 
            // btnApply
            // 
            btnApply.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnApply.BackColor = Color.FromArgb(40, 167, 69);
            btnApply.FlatAppearance.BorderSize = 0;
            btnApply.FlatStyle = FlatStyle.Flat;
            btnApply.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            btnApply.ForeColor = Color.White;
            btnApply.Location = new Point(780, 660);
            btnApply.Name = "btnApply";
            btnApply.Size = new Size(90, 40);
            btnApply.TabIndex = 3;
            btnApply.Text = "应用";
            btnApply.UseVisualStyleBackColor = false;
            btnApply.Click += BtnApply_Click;
            // 
            // lblTitle
            // 
            lblTitle.AutoSize = true;
            lblTitle.Font = new Font("Microsoft YaHei UI", 14F, FontStyle.Bold, GraphicsUnit.Point);
            lblTitle.ForeColor = Color.FromArgb(33, 37, 41);
            lblTitle.Location = new Point(20, 20);
            lblTitle.Name = "lblTitle";
            lblTitle.Size = new Size(88, 26);
            lblTitle.TabIndex = 4;
            lblTitle.Text = "页面设置";
            // 
            // PageSetupForm
            // 
            AutoScaleDimensions = new SizeF(8F, 19F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(248, 249, 250);
            ClientSize = new Size(900, 720);
            Controls.Add(lblTitle);
            Controls.Add(btnApply);
            Controls.Add(btnCancel);
            Controls.Add(btnOK);
            Controls.Add(tabControl);
            Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "PageSetupForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "页面设置";
            tabControl.ResumeLayout(false);
            tabPageSize.ResumeLayout(false);
            groupBoxSlideSize.ResumeLayout(false);
            groupBoxSlideSize.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numWidth).EndInit();
            ((System.ComponentModel.ISupportInitialize)numHeight).EndInit();
            groupBoxAspectRatio.ResumeLayout(false);
            groupBoxAspectRatio.PerformLayout();
            tabPageOrientation.ResumeLayout(false);
            groupBoxOrientation.ResumeLayout(false);
            groupBoxOrientation.PerformLayout();
            tabPageBackground.ResumeLayout(false);
            groupBoxBackground.ResumeLayout(false);
            groupBoxBackground.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private System.Windows.Forms.TabControl tabControl;
        private System.Windows.Forms.TabPage tabPageSize;
        private System.Windows.Forms.TabPage tabPageOrientation;
        private System.Windows.Forms.TabPage tabPageBackground;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnApply;
        private System.Windows.Forms.Label lblTitle;

        // 幻灯片尺寸设置控件
        private System.Windows.Forms.GroupBox groupBoxSlideSize;
        private System.Windows.Forms.CheckBox chkEnablePresetSizes;
        private System.Windows.Forms.RadioButton radioStandard;
        private System.Windows.Forms.RadioButton radioWidescreen;
        private System.Windows.Forms.RadioButton radioCustom;
        private System.Windows.Forms.CheckBox chkEnableSizeType;
        private System.Windows.Forms.Label lblWidth;
        private System.Windows.Forms.Label lblHeight;
        private System.Windows.Forms.NumericUpDown numWidth;
        private System.Windows.Forms.NumericUpDown numHeight;
        private System.Windows.Forms.CheckBox chkEnableCustomSize;
        private System.Windows.Forms.Label lblWidthUnit;
        private System.Windows.Forms.Label lblHeightUnit;
        private System.Windows.Forms.ComboBox comboWidthUnit;
        private System.Windows.Forms.ComboBox comboHeightUnit;
        private System.Windows.Forms.Label lblPresetSizes;
        private System.Windows.Forms.ComboBox comboPresetSizes;

        // 幻灯片方向设置控件
        private System.Windows.Forms.GroupBox groupBoxOrientation;
        private System.Windows.Forms.CheckBox chkEnableOrientation;
        private System.Windows.Forms.RadioButton radioLandscape;
        private System.Windows.Forms.RadioButton radioPortrait;
        private System.Windows.Forms.Label lblOrientationDescription;
        private System.Windows.Forms.Label lblLandscapeDescription;
        private System.Windows.Forms.Label lblPortraitDescription;

        // 尺寸比例调整控件
        private System.Windows.Forms.GroupBox groupBoxAspectRatio;
        private System.Windows.Forms.CheckBox chkEnableAspectRatio;
        private System.Windows.Forms.Label lblCurrentRatio;
        private System.Windows.Forms.Label lblRatioValue;
        private System.Windows.Forms.CheckBox chkMaintainRatio;

        // 背景设置控件
        private System.Windows.Forms.GroupBox groupBoxBackground;
        private System.Windows.Forms.CheckBox chkEnableNoBackground;
        private System.Windows.Forms.CheckBox chkEnableSolidColor;
        private System.Windows.Forms.CheckBox chkEnableGradient;
        private System.Windows.Forms.CheckBox chkEnableImageBackground;
        private System.Windows.Forms.RadioButton radioSolidColor;
        private System.Windows.Forms.RadioButton radioGradient;
        private System.Windows.Forms.RadioButton radioImage;
        private System.Windows.Forms.RadioButton radioNoBackground;
        private System.Windows.Forms.Panel panelColorPicker;
        private System.Windows.Forms.Button btnSelectColor;
        private System.Windows.Forms.Label lblBackgroundColor;
        private System.Windows.Forms.Panel panelGradientStart;
        private System.Windows.Forms.Panel panelGradientEnd;
        private System.Windows.Forms.Button btnSelectGradientStart;
        private System.Windows.Forms.Button btnSelectGradientEnd;
        private System.Windows.Forms.Label lblGradientStart;
        private System.Windows.Forms.Label lblGradientEnd;
        private System.Windows.Forms.Button btnSelectImage;
        private System.Windows.Forms.Label lblImagePath;
        private System.Windows.Forms.ComboBox comboImageFillMode;
        private System.Windows.Forms.Label lblImageFillMode;


    }
}

using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 文件名替换设置窗体 - 用于配置文件名模式替换功能，支持规则管理、导入导出等操作
    /// </summary>
    public partial class FilenameReplacementForm : Form
    {
        #region 字段和属性

        private readonly FilenameReplacementSettings _settings;
        private TabControl _tabControl = null!;
        private Panel mainPanel = null!;
        private TabPage patternReplacementTab = null!;
        private Panel buttonPanel = null!;
        private Button btnOK = null!;
        private Button btnCancel = null!;
        private readonly Dictionary<string, Panel> _tabPanels = new Dictionary<string, Panel>();

        /// <summary>
        /// 获取当前设置
        /// </summary>
        public FilenameReplacementSettings Settings => _settings;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化文件名替换设置窗体
        /// </summary>
        public FilenameReplacementForm() : this(new FilenameReplacementSettings())
        {
        }

        /// <summary>
        /// 初始化文件名替换设置窗体
        /// </summary>
        /// <param name="settings">文件名替换设置</param>
        public FilenameReplacementForm(FilenameReplacementSettings settings)
        {
            _settings = settings ?? new FilenameReplacementSettings();
            InitializeComponent();
            InitializeControls();
            LoadSettings();

            // 添加窗体关闭事件处理 - 确保直接关闭窗口时也能保存配置
            this.FormClosing += FilenameReplacementForm_FormClosing;
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FilenameReplacementForm));
            mainPanel = new Panel();
            _tabControl = new TabControl();
            patternReplacementTab = new TabPage();
            buttonPanel = new Panel();
            btnOK = new Button();
            btnCancel = new Button();
            mainPanel.SuspendLayout();
            _tabControl.SuspendLayout();
            buttonPanel.SuspendLayout();
            SuspendLayout();
            //
            // mainPanel
            //
            mainPanel.Controls.Add(_tabControl);
            mainPanel.Controls.Add(buttonPanel);
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Name = "mainPanel";
            mainPanel.TabIndex = 0;
            mainPanel.BackColor = Color.White;
            //
            // _tabControl
            //
            _tabControl.Controls.Add(patternReplacementTab);
            _tabControl.Location = new Point(10, 10);
            _tabControl.Name = "_tabControl";
            _tabControl.SelectedIndex = 0;
            _tabControl.Size = new Size(1364, 890);
            _tabControl.TabIndex = 0;
            _tabControl.Font = new Font("Microsoft YaHei UI", 10F);
            //
            // patternReplacementTab
            //
            patternReplacementTab.Location = new Point(4, 29);
            patternReplacementTab.Name = "patternReplacementTab";
            patternReplacementTab.Size = new Size(1356, 857);
            patternReplacementTab.TabIndex = 0;
            patternReplacementTab.Text = "文件名替换";
            patternReplacementTab.UseVisualStyleBackColor = true;
            patternReplacementTab.BackColor = Color.White;
            //
            // buttonPanel
            //
            buttonPanel.Controls.Add(btnOK);
            buttonPanel.Controls.Add(btnCancel);
            buttonPanel.Location = new Point(10, 910);
            buttonPanel.Name = "buttonPanel";
            buttonPanel.Size = new Size(1364, 50);
            buttonPanel.TabIndex = 1;
            buttonPanel.BackColor = Color.White;
            //
            // btnOK
            //
            btnOK.Location = new Point(1180, 10);
            btnOK.Name = "btnOK";
            btnOK.Size = new Size(80, 30);
            btnOK.TabIndex = 0;
            btnOK.Text = "确定";
            btnOK.UseVisualStyleBackColor = true;
            btnOK.Font = new Font("Microsoft YaHei UI", 10F);
            btnOK.Click += BtnOK_Click;
            //
            // btnCancel
            //
            btnCancel.Location = new Point(1270, 10);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(80, 30);
            btnCancel.TabIndex = 1;
            btnCancel.Text = "取消";
            btnCancel.UseVisualStyleBackColor = true;
            btnCancel.Font = new Font("Microsoft YaHei UI", 10F);
            // 
            // FilenameReplacementForm
            // 
            AcceptButton = btnOK;
            BackColor = Color.White;
            CancelButton = btnCancel;
            ClientSize = new Size(1384, 961);
            Controls.Add(mainPanel);
            Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon?)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "FilenameReplacementForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "文件名替换设置";
            mainPanel.ResumeLayout(false);
            _tabControl.ResumeLayout(false);
            buttonPanel.ResumeLayout(false);
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            try
            {
                InitializePatternReplacementTab();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化控件时发生错误: {ex.Message}\n\n堆栈跟踪:\n{ex.StackTrace}", "初始化错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化文件名模式替换标签页
        /// </summary>
        private void InitializePatternReplacementTab()
        {
            // 确保TabPage存在
            TabPage? tabPage = null;
            if (_tabControl.TabPages.Count > 0)
            {
                tabPage = _tabControl.TabPages[0];
            }
            else
            {
                // 如果没有TabPage，创建一个
                tabPage = patternReplacementTab;
                if (!_tabControl.TabPages.Contains(tabPage))
                {
                    _tabControl.TabPages.Add(tabPage);
                }
            }

            var panel = CreateScrollablePanel();
            tabPage.Controls.Add(panel);
            _tabPanels["PatternReplacement"] = panel;

            int yPos = 20;  // 增加起始位置

            // 总开关 - 控制是否启用文件名模式替换功能，关闭后所有替换规则都不会生效
            var chkMasterSwitch = CreateCheckBox("启用文件名替换功能", 20, yPos);
            chkMasterSwitch.Name = "chkPatternReplacementMaster";
            chkMasterSwitch.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Bold);  // 增大字体
            chkMasterSwitch.Size = new Size(300, 28);  // 增加尺寸
            panel.Controls.Add(chkMasterSwitch);
            yPos += 50;  // 增加间距

            // 规则列表区域 - 调整高度以容纳两行按钮
            var grpRules = CreateGroupBox("文件名替换规则", 20, yPos, 1340, 750);  // 增加高度以容纳更多行

            // 规则列表 - 增加高度以显示更多行
            var listViewRules = new ListView
            {
                Name = "listViewPatternRules",
                Location = new Point(20, 35),  // 调整位置
                Size = new Size(1300, 520),  // 增加高度以显示13行（增加2行）
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                CheckBoxes = true,
                Font = new Font("Microsoft YaHei UI", 11F),  // 增大字体以增加行高
                Scrollable = true,  // 启用滚动条
                MultiSelect = true,  // 支持多选
                HideSelection = false,  // 失去焦点时保持选中状态
                VirtualMode = false,  // 使用标准模式以确保滚动条正常工作
                OwnerDraw = true  // 启用自定义绘制以控制行高
            };

            // 设置自定义行高
            listViewRules.DrawItem += (sender, e) =>
            {
                e.DrawDefault = true;
            };

            listViewRules.DrawSubItem += (sender, e) =>
            {
                e.DrawDefault = true;
            };

            listViewRules.DrawColumnHeader += (sender, e) =>
            {
                e.DrawDefault = true;
            };

            // 通过创建一个较大的ImageList来强制增加行高
            var imageList = new ImageList
            {
                ImageSize = new Size(1, 35),  // 设置较大的行高（35像素）
                ColorDepth = ColorDepth.Depth32Bit
            };
            imageList.Images.Add(new Bitmap(1, 35));  // 添加一个透明图像
            listViewRules.SmallImageList = imageList;

            // 添加列 - 调整列宽以适应更大的ListView，去掉创建时间列，表格内容居中显示
            listViewRules.Columns.Add("启用", 70, HorizontalAlignment.Center);
            listViewRules.Columns.Add("规则名称", 240, HorizontalAlignment.Center);  // 增加宽度
            listViewRules.Columns.Add("匹配类型", 120, HorizontalAlignment.Center);
            listViewRules.Columns.Add("源文本", 250, HorizontalAlignment.Center);  // 增加宽度
            listViewRules.Columns.Add("目标文本", 350, HorizontalAlignment.Center);  // 增加宽度
            listViewRules.Columns.Add("区分大小写", 130, HorizontalAlignment.Center);  // 增加宽度
            listViewRules.Columns.Add("包含扩展名", 130, HorizontalAlignment.Center);  // 增加宽度

            grpRules.Controls.Add(listViewRules);

            // 按钮区域 - 移动到列表下方，两行显示
            int btnY = 570;  // 调整按钮位置，紧贴列表下方（ListView高度520+35+15间距）
            int btnSpacing = 12;  // 增加按钮间距
            int btnCount = 6; // 第一行6个按钮
            int availableWidth = 1300; // 分组框宽度1340 - 左右边距40
            int totalSpacing = btnSpacing * (btnCount - 1); // 5个间距
            int btnWidth = (availableWidth - totalSpacing) / btnCount; // 计算按钮宽度
            int btnHeight = 35;  // 增加按钮高度
            int startX = 20;  // 调整起始位置

            // 第一行按钮布局
            var btnAdd = CreateButton("添加", startX, btnY, btnWidth, btnHeight);
            btnAdd.Name = "btnAddPatternRule";

            var btnEdit = CreateButton("编辑", startX + (btnWidth + btnSpacing), btnY, btnWidth, btnHeight);
            btnEdit.Name = "btnEditPatternRule";

            var btnDelete = CreateButton("删除", startX + (btnWidth + btnSpacing) * 2, btnY, btnWidth, btnHeight);
            btnDelete.Name = "btnDeletePatternRule";

            var btnBatchDelete = CreateButton("批量删除", startX + (btnWidth + btnSpacing) * 3, btnY, btnWidth, btnHeight);
            btnBatchDelete.Name = "btnBatchDeletePatternRules";

            var btnSelectAll = CreateButton("全选", startX + (btnWidth + btnSpacing) * 4, btnY, btnWidth, btnHeight);
            btnSelectAll.Name = "btnSelectAllPatternRules";

            var btnDeselectAll = CreateButton("取消全选", startX + (btnWidth + btnSpacing) * 5, btnY, btnWidth, btnHeight);
            btnDeselectAll.Name = "btnDeselectAllPatternRules";

            // 第二行按钮布局
            int btnY2 = btnY + btnHeight + 8; // 第二行位置，减少间距
            int btnCount2 = 5; // 第二行5个按钮
            int totalSpacing2 = btnSpacing * (btnCount2 - 1); // 4个间距
            int btnWidth2 = (availableWidth - totalSpacing2) / btnCount2; // 计算按钮宽度

            var btnMoveUp = CreateButton("上移", startX, btnY2, btnWidth2, btnHeight);
            btnMoveUp.Name = "btnMoveUpPatternRule";

            var btnMoveDown = CreateButton("下移", startX + (btnWidth2 + btnSpacing), btnY2, btnWidth2, btnHeight);
            btnMoveDown.Name = "btnMoveDownPatternRule";

            var btnDownloadTemplate = CreateButton("下载模板", startX + (btnWidth2 + btnSpacing) * 2, btnY2, btnWidth2, btnHeight);
            btnDownloadTemplate.Name = "btnDownloadTemplate";

            var btnImport = CreateButton("导入", startX + (btnWidth2 + btnSpacing) * 3, btnY2, btnWidth2, btnHeight);
            btnImport.Name = "btnImportPatternRules";

            var btnExport = CreateButton("导出", startX + (btnWidth2 + btnSpacing) * 4, btnY2, btnWidth2, btnHeight);
            btnExport.Name = "btnExportPatternRules";

            // 添加所有按钮到分组框
            grpRules.Controls.Add(btnAdd);
            grpRules.Controls.Add(btnEdit);
            grpRules.Controls.Add(btnDelete);
            grpRules.Controls.Add(btnBatchDelete);
            grpRules.Controls.Add(btnSelectAll);
            grpRules.Controls.Add(btnDeselectAll);
            grpRules.Controls.Add(btnMoveUp);
            grpRules.Controls.Add(btnMoveDown);
            grpRules.Controls.Add(btnDownloadTemplate);
            grpRules.Controls.Add(btnImport);
            grpRules.Controls.Add(btnExport);

            // 绑定事件
            btnAdd.Click += BtnAddPatternRule_Click;
            btnEdit.Click += BtnEditPatternRule_Click;
            btnDelete.Click += BtnDeletePatternRule_Click;
            btnBatchDelete.Click += BtnBatchDeletePatternRules_Click;
            btnSelectAll.Click += BtnSelectAllPatternRules_Click;
            btnDeselectAll.Click += BtnDeselectAllPatternRules_Click;
            btnMoveUp.Click += BtnMoveUpPatternRule_Click;
            btnMoveDown.Click += BtnMoveDownPatternRule_Click;
            btnDownloadTemplate.Click += BtnDownloadTemplate_Click;
            btnImport.Click += BtnImportPatternRules_Click;
            btnExport.Click += BtnExportPatternRules_Click;

            // 绑定列表双击事件
            listViewRules.DoubleClick += (s, e) => BtnEditPatternRule_Click(s, e);

            panel.Controls.Add(grpRules);
        }



        #endregion

        #region 辅助方法

        /// <summary>
        /// 创建面板
        /// </summary>
        private static Panel CreateScrollablePanel()
        {
            return new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = false,  // 移除窗体级别的滚动条
                Padding = new Padding(5),
                BackColor = Color.White
            };
        }

        /// <summary>
        /// 创建复选框
        /// </summary>
        private static CheckBox CreateCheckBox(string text, int x, int y)
        {
            return new CheckBox
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(300, 28),  // 增大尺寸
                UseVisualStyleBackColor = true,
                Font = new Font("Microsoft YaHei UI", 10F)  // 增大字体
            };
        }

        /// <summary>
        /// 创建分组框
        /// </summary>
        private static GroupBox CreateGroupBox(string text, int x, int y, int width, int height)
        {
            return new GroupBox
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, height),
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Bold),  // 增大字体
                Padding = new Padding(10)  // 增加内边距
            };
        }

        /// <summary>
        /// 创建按钮
        /// </summary>
        private static Button CreateButton(string text, int x, int y, int width, int height)
        {
            return new Button
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, height),
                UseVisualStyleBackColor = true,
                Font = new Font("Microsoft YaHei UI", 10F),  // 字体不少于10F
                TextAlign = ContentAlignment.MiddleCenter  // 确保文字居中
            };
        }

        #endregion

        #region 设置加载和保存

        /// <summary>
        /// 加载设置
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                // 加载文件名模式替换设置
                LoadPatternReplacementSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载设置时发生错误: {ex.Message}\n\n堆栈跟踪:\n{ex.StackTrace}", "加载设置错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 加载文件名模式替换设置
        /// </summary>
        private void LoadPatternReplacementSettings()
        {
            try
            {
                if (!_tabPanels.ContainsKey("PatternReplacement"))
                {
                    return;
                }

                var panel = _tabPanels["PatternReplacement"];
                var chkMaster = panel.Controls.Find("chkPatternReplacementMaster", true).FirstOrDefault() as CheckBox;
                if (chkMaster != null)
                {
                    chkMaster.Checked = _settings.PatternReplacement.IsEnabled;
                }

                // 加载规则列表
                LoadPatternReplacementRules();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载文件名模式替换设置时发生错误: {ex.Message}", "加载设置错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        /// <summary>
        /// 加载文件名模式替换规则
        /// </summary>
        private void LoadPatternReplacementRules()
        {
            var panel = _tabPanels["PatternReplacement"];
            var listView = panel.Controls.Find("listViewPatternRules", true).FirstOrDefault() as ListView;
            if (listView == null) return;

            listView.Items.Clear();
            foreach (var rule in _settings.PatternReplacement.ReplacementRules)
            {
                var item = new ListViewItem();
                item.Checked = rule.IsEnabled;
                item.ImageIndex = 0;  // 使用ImageList中的第一个图像来增加行高
                item.SubItems.Add(rule.RuleName);
                item.SubItems.Add(GetMatchTypeDisplayName(rule.MatchType));
                item.SubItems.Add(rule.SourcePattern);
                item.SubItems.Add(rule.TargetPattern);
                item.SubItems.Add(rule.CaseSensitive ? "是" : "否");
                item.SubItems.Add(rule.IncludeExtension ? "是" : "否");
                // 移除创建时间列数据
                item.Tag = rule;
                listView.Items.Add(item);
            }
        }



        /// <summary>
        /// 获取匹配类型显示名称
        /// </summary>
        private static string GetMatchTypeDisplayName(FilenameMatchType matchType)
        {
            return matchType switch
            {
                FilenameMatchType.Wildcard => "文本替换",
                FilenameMatchType.Regex => "正则匹配",
                _ => "未知"
            };
        }



        #endregion

        #region 事件处理

        /// <summary>
        /// 窗体关闭事件处理 - 确保直接关闭窗口时也能保存配置
        /// </summary>
        private void FilenameReplacementForm_FormClosing(object? sender, FormClosingEventArgs e)
        {
            // 如果是通过确定按钮关闭，已经保存过了，不需要重复保存
            if (this.DialogResult == DialogResult.OK)
                return;

            // 如果是通过取消按钮或直接关闭窗口，根据需要决定是否保存
            // 根据用户需求：点击确定按钮或直接关闭窗口时自动保存，点击取消按钮时不保存
            if (this.DialogResult != DialogResult.Cancel)
            {
                try
                {
                    SaveSettings();
                    ConfigService.Instance.UpdateFilenameReplacementSettings(_settings);
                }
                catch (Exception ex)
                {
                    // 保存失败时询问用户是否继续关闭
                    var result = MessageBox.Show($"保存设置时发生错误: {ex.Message}\n\n是否仍要关闭窗口？", "保存错误",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                    if (result == DialogResult.No)
                    {
                        e.Cancel = true; // 取消关闭操作
                    }
                }
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                SaveSettings();
                ConfigService.Instance.UpdateFilenameReplacementSettings(_settings);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.None;
            }
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        private void SaveSettings()
        {
            SavePatternReplacementSettings();
        }

        /// <summary>
        /// 保存文件名模式替换设置
        /// </summary>
        private void SavePatternReplacementSettings()
        {
            var panel = _tabPanels["PatternReplacement"];

            var chkMaster = panel.Controls.Find("chkPatternReplacementMaster", true).FirstOrDefault() as CheckBox;
            if (chkMaster != null)
            {
                _settings.PatternReplacement.IsEnabled = chkMaster.Checked;
            }

            // 保存规则启用状态
            SavePatternReplacementRulesState();
        }



        /// <summary>
        /// 保存文件名模式替换规则状态
        /// </summary>
        private void SavePatternReplacementRulesState()
        {
            var panel = _tabPanels["PatternReplacement"];
            var listView = panel.Controls.Find("listViewPatternRules", true).FirstOrDefault() as ListView;
            if (listView == null) return;

            foreach (ListViewItem item in listView.Items)
            {
                if (item.Tag is FilenamePatternReplacementRule rule)
                {
                    rule.IsEnabled = item.Checked;
                }
            }
        }



        #region 文件名模式替换规则事件

        /// <summary>
        /// 添加文件名模式替换规则
        /// </summary>
        private void BtnAddPatternRule_Click(object? sender, EventArgs e)
        {
            try
            {
                using var ruleForm = new FilenamePatternReplacementRuleForm();
                if (ruleForm.ShowDialog(this) == DialogResult.OK)
                {
                    _settings.PatternReplacement.ReplacementRules.Add(ruleForm.Rule);
                    LoadPatternReplacementRules();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加文件名模式替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑文件名模式替换规则
        /// </summary>
        private void BtnEditPatternRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var panel = _tabPanels["PatternReplacement"];
                var listView = panel.Controls.Find("listViewPatternRules", true).FirstOrDefault() as ListView;
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is FilenamePatternReplacementRule rule)
                    {
                        using var ruleForm = new FilenamePatternReplacementRuleForm(rule);
                        if (ruleForm.ShowDialog(this) == DialogResult.OK)
                        {
                            LoadPatternReplacementRules();
                        }
                    }
                }
                else
                {
                    MessageBox.Show("请选择要编辑的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"编辑文件名模式替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除文件名模式替换规则 - 直接删除选中的规则，无需确认对话框
        /// </summary>
        private void BtnDeletePatternRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var panel = _tabPanels["PatternReplacement"];
                var listView = panel.Controls.Find("listViewPatternRules", true).FirstOrDefault() as ListView;
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItems = listView.SelectedItems.Cast<ListViewItem>().ToList();
                    foreach (var item in selectedItems)
                    {
                        if (item.Tag is FilenamePatternReplacementRule rule)
                        {
                            _settings.PatternReplacement.ReplacementRules.Remove(rule);
                        }
                    }
                    LoadPatternReplacementRules();
                }
                else
                {
                    MessageBox.Show("请选择要删除的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除文件名模式替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导入文件名模式替换规则
        /// </summary>
        private void BtnImportPatternRules_Click(object? sender, EventArgs e)
        {
            try
            {
                using var openFileDialog = new OpenFileDialog
                {
                    Title = "导入文件名替换规则",
                    Filter = "Excel文件 (*.xlsx)|*.xlsx|JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                    DefaultExt = "xlsx",
                    Multiselect = false
                };

                if (openFileDialog.ShowDialog(this) == DialogResult.OK)
                {
                    var extension = Path.GetExtension(openFileDialog.FileName).ToLower();

                    if (extension == ".xlsx" || extension == ".xls")
                    {
                        // 从Excel文件导入
                        var excelService = new Services.ExcelImportExportService();
                        var importedRules = excelService.ImportFilenameReplacementRules(openFileDialog.FileName);

                        if (importedRules != null && importedRules.Count > 0)
                        {
                            foreach (var rule in importedRules)
                            {
                                _settings.PatternReplacement.ReplacementRules.Add(rule);
                            }
                            LoadPatternReplacementRules();
                            MessageBox.Show($"成功导入 {importedRules.Count} 条规则", "导入成功",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("Excel文件中没有找到有效的规则", "导入失败",
                                MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                    else if (extension == ".json")
                    {
                        // 从JSON文件导入
                        var jsonContent = File.ReadAllText(openFileDialog.FileName);
                        var importedRules = System.Text.Json.JsonSerializer.Deserialize<FilenamePatternReplacementRule[]>(jsonContent);

                        if (importedRules != null && importedRules.Length > 0)
                        {
                            foreach (var rule in importedRules)
                            {
                                _settings.PatternReplacement.ReplacementRules.Add(rule);
                            }
                            LoadPatternReplacementRules();
                            MessageBox.Show($"成功导入 {importedRules.Length} 条规则", "导入成功",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("JSON文件中没有找到有效的规则", "导入失败",
                                MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                    else
                    {
                        MessageBox.Show("不支持的文件格式，请选择Excel文件(.xlsx)或JSON文件(.json)", "格式错误",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入文件失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导出文件名模式替换规则
        /// </summary>
        private void BtnExportPatternRules_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_settings.PatternReplacement.ReplacementRules.Count == 0)
                {
                    MessageBox.Show("没有可导出的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                using var saveFileDialog = new SaveFileDialog
                {
                    Title = "导出文件名替换规则",
                    Filter = "Excel文件 (*.xlsx)|*.xlsx|JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                    DefaultExt = "xlsx",
                    FileName = $"文件名替换规则_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveFileDialog.ShowDialog(this) == DialogResult.OK)
                {
                    var extension = Path.GetExtension(saveFileDialog.FileName).ToLower();

                    if (extension == ".xlsx" || extension == ".xls")
                    {
                        // 导出为Excel格式
                        var excelService = new Services.ExcelImportExportService();
                        excelService.ExportFilenameReplacementRules(_settings.PatternReplacement.ReplacementRules, saveFileDialog.FileName);
                    }
                    else
                    {
                        // 导出为JSON格式
                        var jsonContent = System.Text.Json.JsonSerializer.Serialize(_settings.PatternReplacement.ReplacementRules.ToArray(),
                            new System.Text.Json.JsonSerializerOptions { WriteIndented = true, Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping });
                        File.WriteAllText(saveFileDialog.FileName, jsonContent);
                    }

                    MessageBox.Show("导出成功！", "导出完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出文件失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 批量删除文件名模式替换规则 - 直接删除勾选的规则，无需确认对话框
        /// </summary>
        private void BtnBatchDeletePatternRules_Click(object? sender, EventArgs e)
        {
            try
            {
                var panel = _tabPanels["PatternReplacement"];
                var listView = panel.Controls.Find("listViewPatternRules", true).FirstOrDefault() as ListView;
                if (listView == null) return;

                var checkedItems = listView.Items.Cast<ListViewItem>().Where(item => item.Checked).ToList();
                if (checkedItems.Count == 0)
                {
                    MessageBox.Show("请先选择要删除的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                foreach (var item in checkedItems)
                {
                    if (item.Tag is FilenamePatternReplacementRule rule)
                    {
                        _settings.PatternReplacement.ReplacementRules.Remove(rule);
                    }
                }
                LoadPatternReplacementRules();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"批量删除文件名模式替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 全选文件名模式替换规则
        /// </summary>
        private void BtnSelectAllPatternRules_Click(object? sender, EventArgs e)
        {
            try
            {
                var panel = _tabPanels["PatternReplacement"];
                var listView = panel.Controls.Find("listViewPatternRules", true).FirstOrDefault() as ListView;
                if (listView == null) return;

                foreach (ListViewItem item in listView.Items)
                {
                    item.Checked = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"全选操作失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消全选文件名模式替换规则
        /// </summary>
        private void BtnDeselectAllPatternRules_Click(object? sender, EventArgs e)
        {
            try
            {
                var panel = _tabPanels["PatternReplacement"];
                var listView = panel.Controls.Find("listViewPatternRules", true).FirstOrDefault() as ListView;
                if (listView == null) return;

                foreach (ListViewItem item in listView.Items)
                {
                    item.Checked = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"取消全选操作失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 上移文件名模式替换规则
        /// </summary>
        private void BtnMoveUpPatternRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var panel = _tabPanels["PatternReplacement"];
                var listView = panel.Controls.Find("listViewPatternRules", true).FirstOrDefault() as ListView;
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    var index = selectedItem.Index;
                    if (index > 0 && selectedItem.Tag is FilenamePatternReplacementRule rule)
                    {
                        _settings.PatternReplacement.ReplacementRules.RemoveAt(index);
                        _settings.PatternReplacement.ReplacementRules.Insert(index - 1, rule);
                        LoadPatternReplacementRules();

                        // 重新选中移动后的项
                        if (index - 1 < listView.Items.Count)
                        {
                            listView.Items[index - 1].Selected = true;
                            listView.Items[index - 1].Focused = true;
                        }
                    }
                }
                else
                {
                    MessageBox.Show("请选择要上移的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"上移规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 下移文件名模式替换规则
        /// </summary>
        private void BtnMoveDownPatternRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var panel = _tabPanels["PatternReplacement"];
                var listView = panel.Controls.Find("listViewPatternRules", true).FirstOrDefault() as ListView;
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    var index = selectedItem.Index;
                    if (index < _settings.PatternReplacement.ReplacementRules.Count - 1 && selectedItem.Tag is FilenamePatternReplacementRule rule)
                    {
                        _settings.PatternReplacement.ReplacementRules.RemoveAt(index);
                        _settings.PatternReplacement.ReplacementRules.Insert(index + 1, rule);
                        LoadPatternReplacementRules();

                        // 重新选中移动后的项
                        if (index + 1 < listView.Items.Count)
                        {
                            listView.Items[index + 1].Selected = true;
                            listView.Items[index + 1].Focused = true;
                        }
                    }
                }
                else
                {
                    MessageBox.Show("请选择要下移的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"下移规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 下载Excel模板
        /// </summary>
        private void BtnDownloadTemplate_Click(object? sender, EventArgs e)
        {
            try
            {
                using var saveFileDialog = new SaveFileDialog
                {
                    Title = "保存Excel模板",
                    Filter = "Excel文件 (*.xlsx)|*.xlsx|所有文件 (*.*)|*.*",
                    DefaultExt = "xlsx",
                    FileName = "文件名替换规则模板.xlsx"
                };

                if (saveFileDialog.ShowDialog(this) == DialogResult.OK)
                {
                    var excelService = new Services.ExcelImportExportService();
                    excelService.CreateFilenameReplacementTemplate(saveFileDialog.FileName);

                    MessageBox.Show("Excel模板下载成功！\n\n请在模板中填写替换规则，然后使用\"导入\"功能导入规则。",
                        "下载成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"下载Excel模板失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion



        #endregion
    }
}
